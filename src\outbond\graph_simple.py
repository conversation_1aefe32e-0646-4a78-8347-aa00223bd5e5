

from typing import TypedDict

from langgraph.graph import END, START, StateGraph
from outbond.configuration import Configuration
from outbond.nodes import analyzer_node, handle_error_node, plan_node, search_node, scrape_node, analyze_node, complete_node, summarize_node

from outbond.nodes.formatter import formatter_node
from outbond.state import SearchPhase, SearchState



workflow = StateGraph(SearchState, config_schema=Configuration)


workflow.add_node("analyzer", analyzer_node)
workflow.add_node("plan", plan_node)

workflow.add_node("complete", complete_node)

workflow.add_edge(START, "analyzer") 
workflow.add_edge("analyzer", "plan") 
workflow.add_edge("plan", "complete") 
workflow.add_edge("complete", END)


# Compile the graph
graph = workflow.compile()
graph.name = "OutbondSearchAgent" 


# png_image = graph.get_graph().draw_mermaid_png()

# with open("langgraph_diagram.png", "wb") as f:
#     f.write(png_image)
