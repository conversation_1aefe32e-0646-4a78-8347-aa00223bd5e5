2025-07-15 16:26:52 - outbond.logging_config - INFO - setup_logging:77 - Logging configured - File: logs\plan_node_20250715_162652.log, Level: DEBUG
2025-07-15 16:26:52 - outbond.logging_config - INFO - setup_logging:78 - Log rotation: 10MB max, 5 backups
2025-07-15 16:26:52 - outbond.logging_config - INFO - setup_plan_node_logging:110 - Plan node logging configured with detailed debugging enabled
2025-07-15 16:26:52 - langchain_community.utils.user_agent - WARNING - get_user_agent:11 - USER_AGENT environment variable not set, consider setting it to identify your requests.
2025-07-15 16:26:52 - langgraph_api.graph - INFO - register_graph:56 - {'graph_id': 'outbound', 'event': "Registering graph with id 'outbound'", 'thread_name': 'asyncio_1', 'logger': 'langgraph_api.graph', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:26:52.726084Z'}
2025-07-15 16:26:52 - langgraph_api.graph - INFO - stop_remote_graphs:404 - {'event': 'Shutting down remote graphs', 'thread_name': 'MainThread', 'logger': 'langgraph_api.graph', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:26:52.730516Z'}
2025-07-15 16:26:52 - httpcore.connection - DEBUG - atrace:87 - close.started
2025-07-15 16:26:52 - httpcore.connection - DEBUG - atrace:87 - close.complete
2025-07-15 16:26:53 - uvicorn.error - ERROR - send:121 - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\starlette\routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph_runtime_inmem\lifespan.py", line 64, in lifespan
    await graph.collect_graphs_from_env(True)
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph_api\graph.py", line 386, in collect_graphs_from_env
    graph = await run_in_executor(None, _graph_from_spec, spec)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph_api\utils\config.py", line 135, in run_in_executor
    return await asyncio.get_running_loop().run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph_api\utils\config.py", line 126, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph_api\graph.py", line 431, in _graph_from_spec
    modspec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Dev_Lasngrapg\assesment\src/outbond2/graph.py", line 8, in <module>
    from outbond2.configuration import Configuration
  File "C:\Dev_Lasngrapg\assesment\src\outbond2\__init__.py", line 4, in <module>
    from .configuration import Configuration
  File "C:\Dev_Lasngrapg\assesment\src\outbond2\configuration.py", line 10, in <module>
    from outbond2 import prompts
ImportError: cannot import name 'prompts' from partially initialized module 'outbond2' (most likely due to a circular import) (C:\Dev_Lasngrapg\assesment\src\outbond2\__init__.py)
Could not import python module for graph:
GraphSpec(id='outbound2', path='./src/outbond2/graph.py', module=None, variable='graph', config={}, description=None)
This error likely means you haven't installed your project and its dependencies yet. Before running the server, install your project:

If you are using requirements.txt:
python -m pip install -r requirements.txt

If you are using pyproject.toml or setuptools:
python -m pip install -e .

Make sure to run this command from your project's root directory (where your setup.py or pyproject.toml is located)

2025-07-15 16:26:53 - uvicorn.error - ERROR - startup:59 - Application startup failed. Exiting.
