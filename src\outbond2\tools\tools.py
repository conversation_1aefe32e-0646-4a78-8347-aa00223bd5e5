"""Tools for data enrichment.

This module contains functions that are directly exposed to the LLM as tools.
These tools can be used for tasks such as web searching and scraping.
Users can edit and extend these tools as needed.
"""

import json
from typing import Any, Optional, cast

import aiohttp
from langchain_community.tools.tavily_search import TavilySearchResults
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg
from langgraph.prebuilt import InjectedState
from typing_extensions import Annotated

from outbond2.configuration import Configuration
from outbond2.state import Citation, State
from outbond2.utils import extract_title_from_html, init_model
from shared.utils import convert_simple_schema_to_json_schema




async def search(
    query: str, 
    *, 
    state: Annotated[State, InjectedState],
    config: Annotated[RunnableConfig, InjectedToolArg]
) -> Optional[list[dict[str, Any]]]:
    """Query a search engine.

    This function queries the web to fetch comprehensive, accurate, and trusted results. It's particularly useful
    for answering questions about current events. Provide as much context in the query as needed to ensure high recall.
    
    Now includes citation tracking - all search results are automatically added to the state citations.
    """
    try:
        configuration = Configuration.from_runnable_config(config)
        wrapped = TavilySearchResults(max_results=configuration.max_search_results)
        result = await wrapped.ainvoke({"query": query})
        
        # Cast to the expected type
        search_results = cast(list[dict[str, Any]], result)
        
        # Create citations from search results and add to state
        new_citations = []
        for item in search_results:
            # Tavily typically returns: {'url': str, 'content': str, 'title': str, 'score': float}
            citation = Citation(
                url=item.get('url', ''),
                title=item.get('title', 'Search Result'),
                content_snippet=item.get('content', '')[:200] + "..." if len(item.get('content', '')) > 200 else item.get('content', ''),
                source_type="search"
            )
            new_citations.append(citation)
        
        # Add citations to state (the reducer will concatenate them)
        state.citations.extend(new_citations)
        
        return search_results
        
    except Exception as e:
        # Graceful error handling - return empty results but don't crash
        print(f"Search error: {str(e)}")
        return []





_INFO_PROMPT = """You are doing web research on behalf of a user. You are trying to find out this information:

<info>
{info}
</info>

You just scraped the following website: {url}

Based on the website content below, jot down some notes about the website.

<Website content>
{content}
</Website content>"""


async def scrape_website(
    url: str,
    *,
    state: Annotated[State, InjectedState],
    config: Annotated[RunnableConfig, InjectedToolArg],
) -> str:
    """Scrape and summarize content from a given URL.

    Now includes citation tracking - the scraped website is automatically added to state citations.

    Returns:
        str: A summary of the scraped content, tailored to the extraction schema.
    """
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                if response.status != 200:
                    # Add citation even for failed requests
                    citation = Citation(
                        url=url,
                        title=f"Error {response.status}",
                        content_snippet=f"HTTP {response.status} error when accessing this URL",
                        source_type="scrape_error"
                    )
                    state.citations.append(citation)
                    return f"Error accessing {url}: HTTP {response.status}"
                
                content = await response.text()

        # Extract title from HTML content
        title = extract_title_from_html(content)
        
        # Create citation for the scraped website
        citation = Citation(
            url=url,
            title=title,
            content_snippet=content[:300] + "..." if len(content) > 300 else content,
            source_type="scrape"
        )
        state.citations.append(citation)

        # Convert simple schema format to JSON Schema format
        json_schema = convert_simple_schema_to_json_schema(state.extraction_schema)
        
        # Check if this is a text format response
        is_text_format = state.extraction_schema.get("format") == "text"

        if is_text_format:
            p = _INFO_PROMPT.format(
                info="A text response",
                url=url,
                content=content[:40_000],
            )
        else:
            p = _INFO_PROMPT.format(
                info=json.dumps(json_schema, indent=2),
                url=url,
                content=content[:40_000],
            )
        
        raw_model = init_model(config)
        result = await raw_model.ainvoke(p)
        return str(result.content)
        
    except aiohttp.ClientError as e:
        # Network/HTTP errors
        citation = Citation(
            url=url,
            title="Network Error",
            content_snippet=f"Network error when accessing this URL: {str(e)}",
            source_type="scrape_error"
        )
        state.citations.append(citation)
        return f"Network error accessing {url}: {str(e)}"
        
    except Exception as e:
        # Other errors (parsing, etc.)
        citation = Citation(
            url=url,
            title="Scraping Error", 
            content_snippet=f"Error scraping this URL: {str(e)}",
            source_type="scrape_error"
        )
        state.citations.append(citation)
        return f"Error scraping {url}: {str(e)}"
