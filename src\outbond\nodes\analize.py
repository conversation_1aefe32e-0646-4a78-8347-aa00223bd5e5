"""Analyze node for processing sources and checking answered questions."""

import json
import os
from typing import List, Dict, Optional


from shared.llm_factory import ChatOpenRouterAI, create_llm
from outbond.state import SearchState, SearchPhase, Source, SubQuery, SEARCH_CONFIG
from outbond.configuration import Configuration
from langchain_core.messages import SystemMessage, HumanMessage
from langsmith import AsyncClient

async def check_answers_in_sources(
    sub_queries: List[SubQuery],
    sources: List[Source],
    llm: Optional[ChatOpenRouterAI] = None
) -> List[SubQuery]:
    """Check which questions have been answered by the provided sources.
    
    Args:
        sub_queries: List of sub-queries to check
        sources: List of sources to check against
        llm: Optional LLM instance
        
    Returns:
        Updated sub-queries with answer status and confidence
    """
    if not sources:
        return sub_queries
    

   

    # Build questions list
    questions_text = '\n'.join([sq['question'] for sq in sub_queries])
    
    # Build sources text with limited preview for performance
    sources_text_parts = []
    for source in sources[:SEARCH_CONFIG.MAX_SOURCES_TO_CHECK]:
        source_info = f"URL: {source.url}\nTitle: {source.title}\n"
        
        # Include summary if available (this is the key insight from the search)
        if source.summary:
            source_info += f"Summary: {source.summary}\n"
        
        # Include content preview
        if source.content:
            content_preview = source.content[:SEARCH_CONFIG.ANSWER_CHECK_PREVIEW]
            source_info += f"Content: {content_preview}\n"
        
        sources_text_parts.append(source_info)
    
    sources_text = '\n---\n'.join(sources_text_parts)
    
    
        

    
    
    try:
        LANGSMITH_API_KEY = os.getenv('LANGSMITH_API_KEY')
        client = AsyncClient(api_key=LANGSMITH_API_KEY)
        prompt = await client.pull_prompt("check_answers_in_sources", include_model=False)
        
        # Initialize LLM if not provided
        if llm is None:
            llm = create_llm()  
        
        chain = prompt | llm
    
    
        content_prompt=f"Questions to check:\n{questions_text}\n\nSources:\n{sources_text}"
        messages = {"content":content_prompt}
        response = await chain.ainvoke(messages)
        content = str(response.content)
        
        # Strip markdown code blocks if present
        content = content.replace('```json', '').replace('```', '').strip()
        
        results = json.loads(content)
        
        # Update sub-queries with results
        updated_sub_queries = []
        for sq in sub_queries:
            result = next((r for r in results if r['question'] == sq['question']), None)
            if result and result['confidence'] > sq['confidence']:
                updated_sq = sq.copy()
                updated_sq['answered'] = result['confidence'] >= SEARCH_CONFIG.MIN_ANSWER_CONFIDENCE
                updated_sq['answer'] = result.get('answer')
                updated_sq['confidence'] = result['confidence']
                # Combine existing sources with new ones
                existing_sources = set(sq.get('sources', []))
                new_sources = set(result.get('sources', []))
                updated_sq['sources'] = list(existing_sources.union(new_sources))
                updated_sub_queries.append(updated_sq)
            else:
                updated_sub_queries.append(sq)
        
        return updated_sub_queries
        
    except Exception as error:
        print(f'Error checking answers: {error}')
        return sub_queries


async def process_sources(
    query: str,
    sources: List[Source],
    search_queries: List[str]
) -> List[Source]:
    """Process sources for synthesis.
    
    This is a basic implementation that returns sources as-is.
    In a more advanced implementation, this could filter, rank, or enhance sources.
    
    Args:
        query: The original query
        sources: List of sources to process
        search_queries: List of search queries that were used
        
    Returns:
        Processed sources
    """
    # For now, return sources as-is
    # Future enhancements could include:
    # - Ranking sources by relevance
    # - Filtering low-quality sources
    # - Enhancing source metadata
    return sources


async def analyze_node(state: SearchState, config: Optional[Configuration] = None) -> SearchState:
        """Analyze node that combines sources and checks answered questions.
        
        This node:
        1. Combines sources and removes duplicates by URL
        2. Checks which questions have been answered (if subQueries exist)
        3. Handles retry logic based on search attempts and confidence
        4. Processes sources for synthesis
        5. Returns appropriate phase transitions
        
        Args:
            state: The current SearchState
            config: Optional configuration
            
        Returns:
            Updated SearchState with analysis results
        """

        # Combine sources and remove duplicates by URL
        source_map: Dict[str, Source] = {}
        
        # Add all sources (not just those with long content, since summaries contain key info)
        for source in (state.sources or []):
            source_map[source.url] = source
        
        # Add scraped sources (may override with better content)
        for source in (state.scrapedSources or []):
            source_map[source.url] = source
        
        all_sources = list(source_map.values())
        
        # Check which questions have been answered
        if state.subQueries:
            updated_sub_queries = await check_answers_in_sources(state.subQueries, all_sources)
            
            answered_count = len([sq for sq in updated_sub_queries if sq['answered']])
            total_questions = len(updated_sub_queries)
            search_attempt = (state.searchAttempt or 0) + 1
            
            # Check if we have partial answers with decent confidence
            partial_answers = [sq for sq in updated_sub_queries if sq['confidence'] >= 0.3]
            has_partial_info = len(partial_answers) > answered_count
            
            print(f"Found {len(all_sources)} sources with quality information")
            
            # If we haven't found all answers and haven't exceeded attempts, try again
            # BUT stop if we have partial info and already tried 2+ times
            if (answered_count < total_questions and 
                search_attempt < SEARCH_CONFIG.MAX_SEARCH_ATTEMPTS and
                not (has_partial_info and search_attempt >= 2)):
                
                return SearchState(
                    query=state.query,
                    context=state.context,
                    understanding=state.understanding,
                    searchQueries=state.searchQueries,
                    currentSearchIndex=state.currentSearchIndex,
                    sources=all_sources,
                    scrapedSources=state.scrapedSources,
                    processedSources=state.processedSources,
                    finalAnswer=state.finalAnswer,
                    followUpQuestions=state.followUpQuestions,
                    subQueries=updated_sub_queries,
                    searchAttempt=search_attempt,
                    retryCount=state.retryCount,
                    phase=SearchPhase.PLANNING,  # Go back to planning for retry
                    error=state.error,
                    errorType=state.errorType,
                    maxRetries=state.maxRetries
                )
            
            # Otherwise proceed with what we have
            try:
                processed_sources = await process_sources(
                    state.query,
                    all_sources,
                    state.searchQueries or []
                )
                
                return SearchState(
                    query=state.query,
                    context=state.context,
                    understanding=state.understanding,
                    searchQueries=state.searchQueries,
                    currentSearchIndex=state.currentSearchIndex,
                    sources=all_sources,
                    scrapedSources=state.scrapedSources,
                    processedSources=processed_sources,
                    finalAnswer=state.finalAnswer,
                    followUpQuestions=state.followUpQuestions,
                    subQueries=updated_sub_queries,
                    searchAttempt=search_attempt,
                    retryCount=state.retryCount,
                    phase=SearchPhase.SYNTHESIZING,
                    error=state.error,
                    errorType=state.errorType,
                    maxRetries=state.maxRetries
                )
            except Exception:
                return SearchState(
                    query=state.query,
                    context=state.context,
                    understanding=state.understanding,
                    searchQueries=state.searchQueries,
                    currentSearchIndex=state.currentSearchIndex,
                    sources=all_sources,
                    scrapedSources=state.scrapedSources,
                    processedSources=all_sources,  # Fallback to unprocessed sources
                    finalAnswer=state.finalAnswer,
                    followUpQuestions=state.followUpQuestions,
                    subQueries=updated_sub_queries,
                    searchAttempt=search_attempt,
                    retryCount=state.retryCount,
                    phase=SearchPhase.SYNTHESIZING,
                    error=state.error,
                    errorType=state.errorType,
                    maxRetries=state.maxRetries
                )
        else:
            # Fallback for queries without sub-queries
            print(f"Found {len(all_sources)} sources with quality information")
            
            try:
                processed_sources = await process_sources(
                    state.query,
                    all_sources,
                    state.searchQueries or []
                )
                
                return SearchState(
                    query=state.query,
                    context=state.context,
                    understanding=state.understanding,
                    searchQueries=state.searchQueries,
                    currentSearchIndex=state.currentSearchIndex,
                    sources=all_sources,
                    scrapedSources=state.scrapedSources,
                    processedSources=processed_sources,
                    finalAnswer=state.finalAnswer,
                    followUpQuestions=state.followUpQuestions,
                    subQueries=state.subQueries,
                    searchAttempt=state.searchAttempt,
                    retryCount=state.retryCount,
                    phase=SearchPhase.SYNTHESIZING,
                    error=state.error,
                    errorType=state.errorType,
                    maxRetries=state.maxRetries
                )
            except Exception:
                return SearchState(
                    query=state.query,
                    context=state.context,
                    understanding=state.understanding,
                    searchQueries=state.searchQueries,
                    currentSearchIndex=state.currentSearchIndex,
                    sources=all_sources,
                    scrapedSources=state.scrapedSources,
                    processedSources=all_sources,  # Fallback to unprocessed sources
                    finalAnswer=state.finalAnswer,
                    followUpQuestions=state.followUpQuestions,
                    subQueries=state.subQueries,
                    searchAttempt=state.searchAttempt,
                    retryCount=state.retryCount,
                    phase=SearchPhase.SYNTHESIZING,
                    error=state.error,
                    errorType=state.errorType,
                    maxRetries=state.maxRetries
                )
        
