# Logging configuration file for outbound search agent
# You can customize these settings as needed

[LOG_SETTINGS]
# Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=DEBUG

# Log directory (relative to project root)
LOG_DIR=logs

# Enable console logging (true/false)
CONSOLE_LOGGING=true

# Maximum log file size in MB
MAX_FILE_SIZE_MB=10

# Number of backup files to keep
BACKUP_COUNT=5

# Custom log file name (optional, will auto-generate if not set)
# LOG_FILE=custom_plan_node.log

[PLAN_NODE_SETTINGS]
# Specific settings for plan_node logging
PLAN_LOG_LEVEL=DEBUG
PLAN_CONSOLE_LOGGING=true

# Plan-specific log file (optional)
# PLAN_LOG_FILE=plan_node_detailed.log
