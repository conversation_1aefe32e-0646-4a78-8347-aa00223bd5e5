from __future__ import annotations

import os
from typing import Optional


from langchain_core.utils.utils import secret_from_env
from langchain_openai import ChatOpenAI
from pydantic import Field, SecretStr



class ChatOpenRouterAI(ChatOpenAI):
    """OpenRouter-backed ChatOpenAI drop-in replacement."""

    openai_api_key: Optional[SecretStr] = Field(
        default_factory=secret_from_env("OPENROUTER_API_KEY", default=None)
    )

    @property
    def lc_secrets(self) -> dict[str, str]:
        return {"openai_api_key": "OPENROUTER_API_KEY"}

    def __init__(
        self,
        *,
        model: str = "anthropic/claude-3.5-sonnet",
        openai_api_key: Optional[str | SecretStr] = None,
        base_url: str = "https://openrouter.ai/api/v1",
        **kwargs,
    ):
        raw_key = openai_api_key or os.getenv("OPENROUTER_API_KEY")
        if raw_key is None:
            raise ValueError("OPENROUTER_API_KEY not set and no key supplied.")
        super().__init__(base_url=base_url, openai_api_key=raw_key, model=model, **kwargs)


def create_llm(model_name: str = "google/gemini-2.5-flash", **kwargs):
    """Return a ChatOpenRouter instance (future-proof to other providers)."""
    return ChatOpenRouterAI(model=model_name, **kwargs)
