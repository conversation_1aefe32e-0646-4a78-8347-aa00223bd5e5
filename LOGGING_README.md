# Plan Node Logging Configuration

This document explains how to configure and use the logging functionality for the `plan_node` function.

## Overview

The `plan_node` function now includes comprehensive logging that saves to files and optionally displays to console. The logging captures:

- Function entry and exit points
- Input parameters and state information
- Sub-query extraction process
- Alternative query generation for retries
- Decision points and state transitions
- Error handling with stack traces
- Performance and debugging information

## Quick Start

1. **Default Configuration**: The logging is automatically configured when you import the plan_node module. Logs will be saved to the `logs/` directory.

2. **Run the Test**: Execute the test script to see logging in action:
   ```bash
   python test_plan_logging.py
   ```

3. **Check Log Files**: After running, check the `logs/` directory for generated log files.

## Configuration Options

### Method 1: Configuration File (Recommended)

Edit the `logging.conf` file to customize logging settings:

```ini
[LOG_SETTINGS]
LOG_LEVEL=DEBUG
LOG_DIR=logs
CONSOLE_LOGGING=true
MAX_FILE_SIZE_MB=10
BACKUP_COUNT=5

[PLAN_NODE_SETTINGS]
PLAN_LOG_LEVEL=DEBUG
PLAN_CONSOLE_LOGGING=true
```

### Method 2: Environment Variables

Set environment variables to configure logging:

```bash
export LOG_LEVEL=DEBUG
export LOG_DIR=logs
export CONSOLE_LOGGING=true
export LOG_FILE=custom_plan_node.log
```

### Method 3: Programmatic Configuration

```python
from outbond.logging_config import setup_plan_node_logging

# Custom configuration
setup_plan_node_logging(
    log_level="DEBUG",
    log_file="my_plan_logs.log",
    console_logging=True
)
```

## Log Levels

- **DEBUG**: Detailed execution information, variable values, step-by-step processing
- **INFO**: High-level flow information, important state changes
- **WARNING**: Fallback scenarios, recoverable issues
- **ERROR**: Exceptions and critical errors
- **CRITICAL**: System-level failures

## Log File Features

### Automatic Rotation
- Log files automatically rotate when they reach the maximum size (default: 10MB)
- Keeps a configurable number of backup files (default: 5)
- Files are named with timestamps for easy identification

### File Naming Convention
- Default: `plan_node_YYYYMMDD_HHMMSS.log`
- Custom names can be specified in configuration

### Log Format
```
2024-07-15 14:30:25 - outbond.nodes.plan - INFO - plan_node:125 - Starting plan_node execution
2024-07-15 14:30:25 - outbond.nodes.plan - DEBUG - plan_node:126 - Input state - query: 'What is AI?', phase: PLANNING
```

## Example Log Output

```
2024-07-15 14:30:25 - outbond.nodes.plan - INFO - plan_node:125 - Starting plan_node execution
2024-07-15 14:30:25 - outbond.nodes.plan - DEBUG - plan_node:126 - Input state - query: 'What are the latest developments in AI?', phase: PLANNING, searchAttempt: 0
2024-07-15 14:30:25 - outbond.nodes.plan - DEBUG - plan_node:127 - Existing subQueries count: 0
2024-07-15 14:30:25 - outbond.nodes.plan - INFO - extract_sub_queries:24 - Extracting sub-queries from query: 'What are the latest developments in AI?'
2024-07-15 14:30:25 - outbond.nodes.plan - DEBUG - extract_sub_queries:28 - Connecting to LangSmith client for extract_sub_queries prompt
2024-07-15 14:30:26 - outbond.nodes.plan - INFO - extract_sub_queries:45 - Successfully extracted 3 sub-queries
2024-07-15 14:30:26 - outbond.nodes.plan - DEBUG - extract_sub_queries:47 - Sub-query 1: question='What are recent AI breakthroughs?', searchQuery='recent AI breakthroughs 2024'
```

## Troubleshooting

### No Log Files Created
- Check if the `logs/` directory exists and is writable
- Verify the LOG_DIR setting in configuration
- Ensure the application has write permissions

### Large Log Files
- Reduce the log level from DEBUG to INFO or WARNING
- Decrease MAX_FILE_SIZE_MB in configuration
- Increase BACKUP_COUNT to keep more historical files

### Missing Console Output
- Set CONSOLE_LOGGING=true in configuration
- Check if console handler is properly configured

## Integration with Existing Code

The logging is automatically initialized when the plan_node module is imported. No changes are needed to existing code that calls `plan_node()`.

```python
from outbond.nodes.plan import plan_node

# Logging is automatically configured
result = await plan_node(state)
```

## Performance Considerations

- DEBUG level logging may impact performance in production
- Consider using INFO or WARNING levels for production environments
- Log file rotation prevents disk space issues
- Console logging can be disabled for better performance

## Customization

To add logging to other nodes, follow the same pattern:

```python
from outbond.logging_config import get_logger

logger = get_logger(__name__)

def my_function():
    logger.info("Function started")
    logger.debug("Processing data...")
    logger.error("Error occurred", exc_info=True)
```

## File Structure

```
project/
├── logs/                          # Log files directory
│   ├── plan_node_20240715_143025.log
│   └── plan_node_20240715_143025.log.1
├── src/outbond/
│   ├── logging_config.py          # Logging configuration module
│   └── nodes/
│       └── plan.py                # Plan node with logging
├── logging.conf                   # Configuration file
├── test_plan_logging.py          # Test script
└── LOGGING_README.md             # This documentation
```
