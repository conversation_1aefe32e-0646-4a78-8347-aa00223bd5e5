"""SDR Research Orchestrator Node.

This node serves as the main decision-making component that coordinates 
between data gathering and final output generation for SDR workflows.
"""

import json
from typing import Any, Dict, List, Optional, cast

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langchain_core.runnables import RunnableConfig

from outbond2.configuration import Configuration
from outbond2.state import State
from outbond2.tools import scrape_website, search
from outbond2.utils import (
    add_citations_to_response,

    get_citation_summary,
    init_model,
)
from shared.utils import convert_simple_schema_to_json_schema


async def sdr_research_orchestrator(
    state: State, *, config: Optional[RunnableConfig] = None
) -> Dict[str, Any]:
    """Orchestrate SDR research by coordinating between data gathering and final output generation.

    This asynchronous function serves as the main decision-making node that:
    1. Initializes configuration and sets up the 'Info' tool for structured output.
    2. Prepares SDR-focused prompts and research context.
    3. Coordinates between search, scraping, and information extraction tools.
    4. Manages the research workflow and decides when to finalize results.
    5. Handles citation integration and output formatting for SDR use cases.
    """
    # Load configuration from the provided RunnableConfig
    configuration = Configuration.from_runnable_config(config)

    # Convert simple schema format to JSON Schema format
    json_schema = convert_simple_schema_to_json_schema(state.extraction_schema)
    
    # Check if this is a text format response
    is_text_format = state.extraction_schema.get("format") == "text"

    # Define the 'Info' tool, which is the user-defined extraction schema
    # Enhanced to include citation information
    if is_text_format:
        info_tool = {
            "name": "Info",
            "description": "Call this when you have gathered all the relevant info and provide a text response. Citations will be automatically added.",
            "parameters": json_schema,
        }
    else:
        info_tool = {
            "name": "Info",
            "description": "Call this when you have gathered all the relevant info. Use null for any fields where data cannot be found or fulfilled. Citations will be automatically added to the structured output.",
            "parameters": json_schema,
        }

    # Format the prompt defined in prompts.py with the extraction schema and topic
    # Include citation information in the prompt
    citation_summary = get_citation_summary(state.citations)
    
    if is_text_format:
        p = configuration.prompt.format(
            info="A text response", topic=state.topic
        )
    else:
        p = configuration.prompt.format(
            info=json.dumps(json_schema, indent=2), topic=state.topic
        )
    
    # Add citation context to the prompt
    if state.citations:
        p += f"\n\nYou have gathered information from {citation_summary}. All sources will be automatically cited in your response."

    # Create the messages list with the formatted prompt and the previous messages
    messages = [HumanMessage(content=p)] + state.messages

    # Initialize the raw model with the provided configuration and bind the tools
    raw_model = init_model(config)
    model = raw_model.bind_tools([scrape_website, search, info_tool], tool_choice="any")
    response = cast(AIMessage, await model.ainvoke(messages))

    # Initialize info to None
    info = None

    # Check if the response has tool calls
    if response.tool_calls:
        for tool_call in response.tool_calls:
            if tool_call["name"] == "Info":
                # Get the raw info from the tool call
                raw_info = tool_call["args"]
                
                # Add citations to the info based on format
                info_with_citations = add_citations_to_response(
                    raw_info, 
                    state.citations, 
                    is_text_format
                )
                
                info = info_with_citations
                
                # If this is a text format, extract the text_response value if needed
                if is_text_format and isinstance(info, dict) and "text_response" in info:
                    info = info["text_response"]
                break
                
    if info is not None:
        # The agent is submitting their answer;
        # ensure it isn't erroneously attempting to simultaneously perform research
        response.tool_calls = [
            next(tc for tc in response.tool_calls if tc["name"] == "Info")
        ]
    response_messages: List[BaseMessage] = [response]
    if not response.tool_calls:  # If LLM didn't respect the tool_choice
        response_messages.append(
            HumanMessage(content="Please respond by calling one of the provided tools.")
        )
    return {
        "messages": response_messages,
        "info": info,
        # Add 1 to the step count
        "loop_step": 1,
    } 