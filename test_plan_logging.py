#!/usr/bin/env python3
"""Test script to demonstrate plan_node logging functionality."""

import asyncio
import sys
import os
from pathlib import Path

# Add src to path so we can import outbond modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

from outbond.nodes.plan import plan_node
from outbond.state import SearchState, SearchPhase


async def test_plan_node_logging():
    """Test the plan_node function with logging enabled."""
    print("Testing plan_node with file logging...")
    print("Logs will be saved to the 'logs' directory")
    
    # Create a test state
    test_state = SearchState(
        query="What are the latest developments in artificial intelligence and machine learning?",
        phase=SearchPhase.PLANNING,
        searchAttempt=0,
        subQueries=[],
        searchQueries=[],
        currentSearchIndex=0,
        sources=[],
        answer="",
        followUpQuestions=[],
        error=None,
        errorType=None
    )
    
    print(f"\nInput query: {test_state.query}")
    print("Executing plan_node...")
    
    try:
        # Execute the plan_node function
        result = await plan_node(test_state)
        
        print(f"\nResult phase: {result.phase}")
        print(f"Number of sub-queries: {len(result.subQueries) if result.subQueries else 0}")
        print(f"Number of search queries: {len(result.searchQueries) if result.searchQueries else 0}")
        
        if result.subQueries:
            print("\nSub-queries generated:")
            for i, sq in enumerate(result.subQueries, 1):
                print(f"  {i}. Question: {sq['question']}")
                print(f"     Search Query: {sq['searchQuery']}")
        
        if result.error:
            print(f"\nError occurred: {result.error}")
            
    except Exception as e:
        print(f"Exception occurred: {str(e)}")
    
    # Check if log files were created
    logs_dir = Path("logs")
    if logs_dir.exists():
        log_files = list(logs_dir.glob("*.log"))
        if log_files:
            print(f"\nLog files created:")
            for log_file in log_files:
                print(f"  - {log_file}")
                # Show file size
                size = log_file.stat().st_size
                print(f"    Size: {size} bytes")
        else:
            print("\nNo log files found in logs directory")
    else:
        print("\nLogs directory not found")


async def test_retry_scenario():
    """Test plan_node with a retry scenario."""
    print("\n" + "="*50)
    print("Testing retry scenario...")
    
    # Create a test state with retry attempt
    test_state = SearchState(
        query="Complex query about quantum computing applications",
        phase=SearchPhase.PLANNING,
        searchAttempt=1,  # This is a retry
        subQueries=[
            {
                "question": "What are quantum computing applications?",
                "searchQuery": "quantum computing applications",
                "answered": False,
                "answer": None,
                "confidence": 0.2,  # Low confidence, needs retry
                "sources": []
            }
        ],
        searchQueries=[],
        currentSearchIndex=0,
        sources=[],
        answer="",
        followUpQuestions=[],
        error=None,
        errorType=None
    )
    
    print(f"Input query: {test_state.query}")
    print(f"Search attempt: {test_state.searchAttempt}")
    print("Executing plan_node for retry scenario...")
    
    try:
        result = await plan_node(test_state)
        print(f"Result phase: {result.phase}")
        print(f"Updated search queries: {result.searchQueries}")
        
    except Exception as e:
        print(f"Exception occurred: {str(e)}")


if __name__ == "__main__":
    print("Plan Node Logging Test")
    print("=" * 30)
    
    # Run the tests
    asyncio.run(test_plan_node_logging())
    asyncio.run(test_retry_scenario())
    
    print("\nTest completed. Check the logs directory for detailed log files.")
