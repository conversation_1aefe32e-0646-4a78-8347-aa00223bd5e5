
from typing import Optional
from outbond.configuration import Configuration
from outbond.state import <PERSON><PERSON><PERSON>Type, SearchPhase, SearchState


def handle_error_node(state: SearchState, config: Optional[Configuration] = None) -> SearchState:
    """Handle error node that implements retry logic.
    
    Args:
        state: The current SearchState
        config: Optional configuration
        
    Returns:
        Updated SearchState with retry logic applied
    """
    
    
    # Check if we should retry based on retry count
    if (state.retryCount or 0) < (state.maxRetries or config.max_retries ):
        # TODO: Implement fifferent retry strategies based on error type
        ## Example: (TODO move to retry strategy class?)
        retry_phase = SearchPhase.PLANNING if state.errorType == ErrorType.LLM_ERROR       else SearchPhase.ANALYZING
        
        return SearchState(
            # TODO: implement retry logic
            retryCount=(state.retryCount or 0) + 1,
            phase=retry_phase,
            error=state.error,
            errorType= ErrorType.UKNOWN_ERROR,
            
        
        )
    
    # Max retries reached, keep error phase
    return SearchState(
  
        phase=SearchPhase.ERROR,
        error=state.error,
        errorType= ErrorType.UKNOWN_ERROR,
    ) 