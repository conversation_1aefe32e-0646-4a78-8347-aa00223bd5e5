2025-07-15 16:23:45 - outbond.logging_config - INFO - setup_logging:77 - Logging configured - File: logs\plan_node_20250715_162345.log, Level: DEBUG
2025-07-15 16:23:45 - outbond.logging_config - INFO - setup_logging:78 - Log rotation: 10MB max, 5 backups
2025-07-15 16:23:45 - outbond.logging_config - INFO - setup_plan_node_logging:110 - Plan node logging configured with detailed debugging enabled
2025-07-15 16:23:46 - langchain_community.utils.user_agent - WARNING - get_user_agent:11 - USER_AGENT environment variable not set, consider setting it to identify your requests.
2025-07-15 16:23:46 - langgraph_api.graph - INFO - register_graph:56 - {'graph_id': 'outbound', 'event': "Registering graph with id 'outbound'", 'thread_name': 'asyncio_1', 'logger': 'langgraph_api.graph', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:23:46.106278Z'}
2025-07-15 16:23:46 - langgraph_api.graph - INFO - register_graph:56 - {'graph_id': 'outbound2', 'event': "Registering graph with id 'outbound2'", 'thread_name': 'asyncio_1', 'logger': 'langgraph_api.graph', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:23:46.161888Z'}
2025-07-15 16:23:46 - langgraph_runtime_inmem.queue - INFO - queue:72 - {'event': 'Starting 1 background workers', 'thread_name': 'asyncio_0', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:23:46.307226Z'}
2025-07-15 16:23:46 - langgraph_runtime_inmem.queue - INFO - queue:89 - {'max': 1, 'available': 1, 'active': 0, 'event': 'Worker stats', 'thread_name': 'asyncio_1', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:23:46.307226Z'}
2025-07-15 16:23:46 - langgraph_runtime_inmem.queue - INFO - queue:141 - {'event': 'Shutting down background workers', 'thread_name': 'MainThread', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:23:46.509283Z'}
2025-07-15 16:23:46 - langgraph_api.graph - INFO - stop_remote_graphs:404 - {'event': 'Shutting down remote graphs', 'thread_name': 'MainThread', 'logger': 'langgraph_api.graph', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:23:46.509283Z'}
2025-07-15 16:23:46 - httpcore.connection - DEBUG - atrace:87 - close.started
2025-07-15 16:23:46 - httpcore.connection - DEBUG - atrace:87 - close.complete
