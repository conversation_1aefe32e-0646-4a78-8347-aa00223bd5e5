"""Summarize node for generating final answers and follow-up questions."""

import asyncio

import os
from typing import Optional, List, Dict, Callable, Any

from langchain_core.messages import SystemMessage, HumanMessage
from langsmith import AsyncClient

from outbond.shared.llm_factory import create_llm
from outbond.state import SearchState, SearchPhase, ErrorType, Source
from outbond.configuration import Configuration
from shared.utils import get_current_date_context


async def retry_with_backoff(
    operation: Callable,
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0
) -> Any:
    """Retry an async operation with exponential backoff.
    
    Args:
        operation: Async function to retry
        max_retries: Maximum number of retry attempts
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds
        
    Returns:
        Result of the operation
        
    Raises:
        Exception: The last exception if all retries fail
    """
    last_exception = None
    
    for attempt in range(max_retries + 1):
        try:
            return await operation()
        except Exception as e:
            last_exception = e
            error_str = str(e).lower()
            
            # Don't retry for certain error types
            if any(keyword in error_str for keyword in ['invalid', 'authentication', 'permission']):
                raise e
            
            # Check if this is a retryable error (overload, rate limit, timeout)
            is_retryable = any(keyword in error_str for keyword in [
                'overloaded', 'rate limit', 'timeout', '429', '529', '503', '502', '500'
            ])
            
            if not is_retryable or attempt == max_retries:
                raise e
            
            # Calculate delay with exponential backoff and jitter
            delay = min(base_delay * (2 ** attempt), max_delay)
            jitter = delay * 0.1 * (0.5 - asyncio.get_event_loop().time() % 1)
            total_delay = delay + jitter
            
            print(f"API overloaded, retrying in {total_delay:.1f}s (attempt {attempt + 1}/{max_retries})...")
            await asyncio.sleep(total_delay)
    
    if last_exception:
        raise last_exception
    else:
        raise Exception("Operation failed after retries")


async def generate_streaming_answer(
    query: str,
    sources: List[Source],

    context: Optional[List[Dict[str, str]]] = None,
    llm = None
) -> str:
    """Generate a streaming answer based on sources.
    
    Args:
        query: The search query
        sources: List of sources to base the answer on
  
        context: Previous conversation context
        llm: Optional LLM instance
        
    Returns:
        Complete answer text
    """
    # Format sources text
    sources_text = ""
    for i, source in enumerate(sources, 1):
        if not source.content:
            sources_text += f"[{i}] {source.title}\n[No content available]\n\n"
        else:
            sources_text += f"[{i}] {source.title}\n{source.content}\n\n"
    
    # Build context prompt if context exists
    context_prompt = ''
    if context and len(context) > 0:
        context_prompt = '\n\nPrevious conversation for context:\n'
        for c in context:
            response_preview = c['response'][:300] + "..." if len(c['response']) > 300 else c['response']
            context_prompt += f"User: {c['query']}\nAssistant: {response_preview}\n\n"
    
    LANGSMITH_API_KEY = os.getenv('LANGSMITH_API_KEY')
    
    prompt_content = f'Question: "{query}"{context_prompt}\n\nBased on these sources:\n{sources_text}'
   
    message = { "content": prompt_content, "current_date_contex": get_current_date_context()}
   
    client = AsyncClient(api_key=LANGSMITH_API_KEY)
    prompt = await  client.pull_prompt("generate_streaming_answer", include_model=False)
    if llm is None:
        llm = create_llm()
    chain = prompt | llm
    full_text = ''
    
    async def try_streaming():
        nonlocal full_text
        stream = chain.astream(message)
        async for chunk in stream:
            content = chunk.content
            if isinstance(content, str):
                full_text += content
               
        return full_text
    
    async def try_non_streaming():
        nonlocal full_text
        response = await chain.ainvoke(message)
        full_text = str(response.content)
        
        return full_text
    
    try:
        # Try streaming first with retry logic
        return await retry_with_backoff(try_streaming, max_retries=3, base_delay=2.0)
    except Exception:
        # Fallback to non-streaming with retry logic
        try:
            full_text = ''  # Reset for non-streaming attempt
            return await retry_with_backoff(try_non_streaming, max_retries=2, base_delay=1.0)
        except Exception as e:
            raise Exception(f"Failed to generate answer after retries: {str(e)}")


async def generate_follow_up_questions(
    original_query: str,
    answer: str,
    _sources: List[Source],
    context: Optional[List[Dict[str, str]]] = None,
    llm = None
) -> List[str]:
    """Generate follow-up questions based on the query and answer.
    
    Args:
        original_query: The original search query
        answer: The generated answer
        _sources: List of sources (not currently used)
        context: Previous conversation context
        llm: Optional LLM instance
        
    Returns:
        List of follow-up questions
    """
    try:
        # Build context prompt if context exists
        context_prompt = ''
        if context and len(context) > 0:
            context_prompt = '\n\nPrevious conversation topics:\n'
            for c in context:
                context_prompt += f"- {c['query']}\n"
            context_prompt += '\nConsider the full conversation flow when generating follow-ups.\n'
       
        LANGSMITH_API_KEY = os.getenv('LANGSMITH_API_KEY')
        client = AsyncClient(api_key=LANGSMITH_API_KEY)
        prompt = await client.pull_prompt("generate_follow_up_questions", include_model=False) 

        if llm is None:
            llm = create_llm()
          
        chain = prompt | llm

          # Truncate answer for prompt if too long
        answer_summary = answer[:1000] + "..." if len(answer) > 1000 else answer

        prompt_content=f'Original query: "{original_query}"\n\nAnswer summary: {answer_summary}{context_prompt}'
        message = { "content": prompt_content, "current_date_contex": get_current_date_context()}
      

        
        async def get_follow_up_response():
            return await chain.ainvoke(message)
        
        response = await retry_with_backoff(get_follow_up_response, max_retries=2, base_delay=1.0)
        questions = str(response.content).split('\n')
        
        # Filter and clean questions
        filtered_questions = []
        for q in questions:
            q = q.strip()
            if q and len(q) > 0 and len(q) < 80:
                filtered_questions.append(q)
        
        return filtered_questions[:3]  # Return max 3 questions
        
    except Exception as e:
        print(f"Failed to generate follow-up questions: {str(e)}")
        return []


async def summarize_node(state: SearchState, config: Optional[Configuration] = None) -> SearchState:
    """Summarize node that generates final answers and follow-up questions.
    
    Args:
        state: The current SearchState
        config: Optional configuration
        
    Returns:
        Updated SearchState with final answer, follow-up questions, and complete phase
    """
    try:
        # Get sources to use (prefer processed sources)
        sources_to_use = state.processedSources or state.sources or []
           # Generate streaming answer
        answer = await generate_streaming_answer(
            state.query,
            sources_to_use,
        
            state.context
        )



        followUpQuestions = await generate_follow_up_questions(
            state.query,
            answer,
            sources_to_use,
            state.context
        )

        return SearchState(
            finalAnswer=answer,
            followUpQuestions=followUpQuestions,
            phase=SearchPhase.COMPLETE,
        )

        
    except Exception as e:
        return SearchState(
            
            
            phase=SearchPhase.ERROR,
            error=str(e),
            errorType=ErrorType.LLM_ERROR,
       
        )