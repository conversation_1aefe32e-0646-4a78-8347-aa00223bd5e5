# 🚀 **Senior AI Engineer – Technical Test Task**

Congratulations on progressing to the next step in your application to join our team as a Senior AI Engineer at **Outbond**!

This task assesses your practical skills in building advanced AI agents, evaluating architectures, optimizing efficiency, and clearly documenting technical solutions.

---

## ✅ **Task Overview**

Your objective is to migrate the existing [Brightdata MCP](https://github.com/brightdata/brightdata-mcp) into a native **LangGraph multi-agent architecture**.

**Important Notes:**

- **Single-Turn Agent:** The agent should **not** support multi-turn conversations. It receives a single input and returns a complete response.
- **SDR Focus:** The AI agent is designed as an SDR (Sales Development Representative) helper. Its output should be highly relevant, actionable, and directly helpful to SDR professionals. This should be reflected in the system prompt.

---

## 🛠️ **Detailed Requirements**

### 1. **Multi-Agent System Migration**

- **Platform:** LangGraph native Python implementation including the tools.
- **Efficiency & Accuracy:**
    - Optimized for minimal token usage without sacrificing accuracy.
    - Provides quick, precise, and helpful responses to queries.
- **Tools & Sub-Agents:**
    - Clearly separated and logically organized.
- **Flexible Outputs:**
    - Supports plain text and structured data (JSON), selectable by the user.
- **Citation:**
    - Every response must include clear source citations.
- **LangSmith Integration:**
    - Fully instrumented with LangSmith tracing.

### 2. **Prompt Engineering & Optimization (LangSmith-based)**

- Conduct **all prompt engineering exclusively in LangSmith (NOT in code).**
- Clearly demonstrate your iterative optimization:
    - **All prompt iterations** must be documented in LangSmith.
    - Your final selected prompt should be justified by evaluation datasets and performance metrics (accuracy, helpfulness, token efficiency).

### 3. **Error Handling**

- Users must be informed if errors or issues occur.
- Errors should never break or disrupt the overall agent flow or user experience.

### 4. **Architecture Justification**

- Briefly explain your architecture choice in your README.
- Highlight benefits related to efficiency, scalability, accuracy, and token optimization.

### 5. **Scalability Considerations**

- Briefly describe (in your README) how your architecture handles scaling.
- Address concurrent user requests and highlight any potential bottlenecks, including mitigation strategies.

---

## 📦 **Deliverables**

Submit your deliverables clearly structured in a **GitHub repository** with:

### A. **GitHub Repository**

Your repository must include:

- **Clean, well-organized source code** following industry best practices.
- **README.md** clearly covering:
    - Setup instructions (dependencies, environment, LangGraph Studio).
    - Agent architecture overview, workflow, tools explanation.
    - Instructions to run the agent in LangGraph Studio.
    - Paragraphs for architecture justification & scalability considerations.
    - Clearly documented evaluation criteria (accuracy, helpfulness metrics, etc.).

### B. **LangSmith Workspace Access**

- Create a dedicated LangSmith workspace for this task.
- Invite **`<EMAIL>`** as a viewer to your workspace.
- Clearly include:
    - All prompt iterations.
    - Evaluation datasets and metrics clearly labeled.
    - LangSmith tracing data.

### C. **Video Demonstration (5–7 mins max)**

Briefly demonstrate:

- Functionality and responsiveness of your AI agent.
- Iterative prompt optimization in LangSmith.
- Overview of your evaluation process.

## 🛠️ Agent Output explanations and examples:

> The agent must recognize when the user explicitly requests structured output and adapt its answer to match the given JSON structure.
> 
> 
> The output could either be:
> 
> - **Plain text:** for quick human reading.
> - **Structured JSON:** for machine-reading, integrations, or easy parsing.

The user will explicitly specify the desired structure when they want JSON output. This is common for outbound workflows, integrations with CRM, or syncing structured data to tools like Clay.

---

## ✅ **Example 1: Plain Text vs JSON - Lead Research Summary**

### **User Input:**

> Give me a short company summary for Stripe.
> 

### **Plain Text Output:**

> Stripe is a technology company that builds economic infrastructure for the internet. Businesses of all sizes use Stripe to accept payments and manage their businesses online.
> 

---

## ✅ **Example 1: Company Summary (Structured JSON with Data Types)**

### **User Input (Specifying Structured Output):**

```json
{
    "format": "json",
    "fields": {
        "company_name": "string",
        "industry": "string",
        "hq_location": "string",
        "short_description": "string"
    }
}

```

### **Expected JSON Output:**

```json
{
    "company_name": "Stripe",
    "industry": "Financial Technology",
    "hq_location": "San Francisco, California",
    "short_description": "Stripe provides economic infrastructure for online businesses to accept payments and manage finances."
}

```

---

## ✅ **Example 2: Contact Info (Structured JSON with Data Types)**

### **User Input:**

```json
{
    "format": "json",
    "fields": {
        "full_name": "string",
        "position": "string",
        "company": "string",
        "email": "string"
    }
}

```

### **Expected JSON Output:**

```json
{
    "full_name": "John Doe",
    "position": "VP of Sales",
    "company": "Acme Inc.",
    "email": "<EMAIL>"
}

```

---

## ✅ **Example 3: LinkedIn Profile Enrichment (Structured JSON with Data Types)**

### **User Input:**

```json
{
    "format": "json",
    "fields": {
        "full_name": "string",
        "position": "string",
        "company": "string",
        "years_of_experience": "integer",
        "industry_expertise": "string"
    }
}

```

### **Expected JSON Output:**

```json
{
    "full_name": "Sarah Miller",
    "position": "Head of Growth",
    "company": "Notion",
    "years_of_experience": 10,
    "industry_expertise": "SaaS, Go-To-Market Strategies"
}

```

---

## ✅ **Example 4: Outreach Personalization (Structured JSON with Data Types)**

### **User Input:**

```json
{
    "format": "json",
    "fields": {
        "first_name": "string",
        "personalized_hook": "string",
        "company": "string",
        "role": "string"
    }
}

```

### **Expected JSON Output:**

```json
{
    "first_name": "James",
    "personalized_hook": "I saw your work at Salesforce around AI in marketing – impressive!",
    "company": "Salesforce",
    "role": "Marketing Lead"
}

```

---

## ✅ **Example 5: Job Posting for Lead Qualification (Structured JSON with Data Types)**

### **User Input:**

```json
{
    "format": "json",
    "fields": {
        "company": "string",
        "role": "string",
        "location": "string",
        "focus_area": "string"
    }
}

```

### **Expected JSON Output:**

```json
{
    "company": "Shopify",
    "role": "Head of Partnerships",
    "location": "Toronto, Canada",
    "focus_area": "E-commerce SaaS expansion"
}

```

---

## 🚩 **Developer Guidance for Implementation:**

- The agent must **read the field names and data types** from the user’s JSON spec.
- The **response must comply strictly with these fields and data types.**
- If a data type cannot be fulfilled (e.g., missing info), return `null` for that key.

---

## 📊 **Evaluation Criteria (100 Points)**

| Criterion | Points |
| --- | --- |
| ⏱️ **Speed of Completion** | 10 |
| 🔍 **Architecture Design & Justification** | 15 |
| 💻 **Code Quality (Cleanliness & Standards)** | 15 |
| 📐 **Token Efficiency & Prompt Optimization** | 15 |
| 🎯 **Agent Accuracy & SDR-Helpful Response Quality** | 15 |
| 📌 **Structured & Text Output Flexibility** | 5 |
| 📖 **Citation Completeness & Accuracy** | 5 |
| 📈 **Evaluation System (LangSmith-based)** | 10 |
| 🚨 **Error Handling & Stability** | 5 |
| 📑 **Documentation (README + Video clarity)** | 5 |

**Extra Credit:** Early submissions (**before deadline**) will receive **+5 bonus points**.

---

## 🗓️ **Deadline**

The task must be submitted within **3 days** from the time you receive this assignment.

**Early submissions** (before the 3-day deadline) will earn an additional **5 bonus points**.

---

## 📝 **Submission Instructions**

- Submit your GitHub repository link directly to **`<EMAIL>` .**
- Ensure you have invited **`<EMAIL>`** clearly to your LangSmith workspace prior to submission.
- Name your video clearly and upload directly to GitHub or provide a clearly accessible link.

---

We look forward to reviewing your submission. Best of luck!

**– The Outbond Team**