


import os
from typing import Dict, List, Optional
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_openai import ChatOpenAI
from outbond.configuration import Configuration
from shared.utils import  get_current_date_context
from shared.llm_factory import ChatOpenRouterAI, create_llm
from outbond.state import SEARCH_CONFIG, SearchPhase, SearchState
from langsmith import Client
from anthropic import Anthropic

from langsmith.client import Client, convert_prompt_to_anthropic_format

def analyze_query(
    query: str, 
    context: Optional[List[Dict[str, str]]] = None,
    llm: Optional[ChatOpenRouterAI] = None
) -> str:
    """Analyze a search query and explain what the user is looking for.
    
    Args:
        query: The search query to analyze
        context: Previous conversation context (query/response pairs)
        llm: The language model to use for analysis
        
    Returns:
        Analysis of the query explaining what the user wants to know
    """
    # Build context prompt if context exists
    context_prompt = ''
    if context and len(context) > 0:
        context_prompt = '\n\nPrevious conversation:\n'
        for c in context:
            response_preview = c['response'][:SEARCH_CONFIG.CONTEXT_PREVIEW_LENGTH] + "..."
            context_prompt += f"User: {c['query']}\nAssistant: {response_preview}\n\n"
            
    LANGSMITH_API_KEY = os.getenv('LANGSMITH_API_KEY')
  
    client = Client(api_key=LANGSMITH_API_KEY)
    prompt = client.pull_prompt("analyzer_node_sys", include_model=False)
    
    if llm is None:
         llm = create_llm()

    message = {"current_date":get_current_date_context(),"query":query, "context_prompt":context_prompt  }
    chain = prompt | llm
    response = chain.invoke(message)
    
 
  
    
    return str(response.content)

def analyzer_node(state: SearchState,config: Optional[Configuration] = None) -> SearchState:
    """Analyzer node that analyzes the user's query and updates understanding.
    
    Args:
        state: The current SearchState
        
    Returns:
        Updated SearchState with query analysis and updated phase
    """
    
    # llm= create_llm(model_name = "switchpoint/router")
    # response = llm.invoke("What is France's capital")
    
    # understanding = f'Here is my understanding of your query {state.query} -> {response}'

    # Get the query and context from state
    query = state.query
    context = state.context
    
    
    try:
        # Run the analyze_query function (now synchronous)
        understanding = analyze_query(query, context)
            
        # Update state with understanding and move to next phase
        return SearchState(
            understanding=understanding,
            phase=SearchPhase.PLANNING
        )
            
    except Exception as e:
        # Handle errors by updating state with error info
        return SearchState(
            query=state.query,
            context=state.context,
            phase=SearchPhase.ERROR,
            error=f"Error analyzing query: {str(e)}"
        ) 