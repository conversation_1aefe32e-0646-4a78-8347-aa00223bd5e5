"""Define the configurable parameters for the agent."""

from __future__ import annotations

from dataclasses import dataclass, field, fields
from typing import Annotated, Optional

from langchain_core.runnables import RunnableConfig, ensure_config



@dataclass(kw_only=True)
class Configuration:
    """The configuration for the agent."""

    model: Annotated[str, {"__template_metadata__": {"kind": "llm"}}] = field(
        default="anthropic/claude-3-5-sonnet-20240620",  # OpenRouter model format
        metadata={
            "description": "The name of the language model to use for the agent. "
            "Should be in OpenRouter format: provider/model-name."
        },
    )
    
    llm_model_analyzer: str = field(
        default="switchpoint/router",
        metadata={
            "description": "The name of the language model to use for the analyzer node. "
            "Should be in OpenRouter format: provider/model-name."
        },
    )
    
    # Add new field for LLM provider
    llm_provider: str = field(
        default="openrouter",  # "openrouter" or "anthropic"
        metadata={
            "description": "LLM provider to use: 'openrouter' or 'anthropic'"
        },
    )

    max_retries: int = field(
        default=3,
        metadata={
            "description": "The maximum number of retries on errors before failing."
        },
    )

    max_search_results: int = field(
        default=10,
        metadata={
            "description": "The maximum number of search results to return for each search query."
        },
    )

    max_info_tool_calls: int = field(
        default=3,
        metadata={
            "description": "The maximum number of times the Info tool can be called during a single interaction."
        },
    )

    max_loops: int = field(
        default=6,
        metadata={
            "description": "The maximum number of interaction loops allowed before the agent terminates."
        },
    )

    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> Configuration:
        """Load configuration w/ defaults for the given invocation."""
        config = ensure_config(config)
        configurable = config.get("configurable") or {}
        _fields = {f.name for f in fields(cls) if f.init}
        return cls(**{k: v for k, v in configurable.items() if k in _fields})
