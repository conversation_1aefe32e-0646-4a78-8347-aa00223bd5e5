2025-07-15 16:24:16 - outbond.logging_config - INFO - setup_logging:77 - Logging configured - File: logs\plan_node_20250715_162416.log, Level: DEBUG
2025-07-15 16:24:16 - outbond.logging_config - INFO - setup_logging:78 - Log rotation: 10MB max, 5 backups
2025-07-15 16:24:16 - outbond.logging_config - INFO - setup_plan_node_logging:110 - Plan node logging configured with detailed debugging enabled
2025-07-15 16:24:16 - langchain_community.utils.user_agent - WARNING - get_user_agent:11 - USER_AGENT environment variable not set, consider setting it to identify your requests.
2025-07-15 16:24:16 - langgraph_api.graph - INFO - register_graph:56 - {'graph_id': 'outbound', 'event': "Registering graph with id 'outbound'", 'thread_name': 'asyncio_1', 'logger': 'langgraph_api.graph', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:24:16.810097Z'}
2025-07-15 16:24:16 - langgraph_api.graph - INFO - register_graph:56 - {'graph_id': 'outbound2', 'event': "Registering graph with id 'outbound2'", 'thread_name': 'asyncio_1', 'logger': 'langgraph_api.graph', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:24:16.863002Z'}
2025-07-15 16:24:17 - langgraph_runtime_inmem.queue - INFO - queue:72 - {'event': 'Starting 1 background workers', 'thread_name': 'asyncio_0', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:24:17.045830Z'}
2025-07-15 16:24:17 - langgraph_api.graph - INFO - stop_remote_graphs:404 - {'event': 'Shutting down remote graphs', 'thread_name': 'MainThread', 'logger': 'langgraph_api.graph', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:24:17.045830Z'}
2025-07-15 16:24:17 - httpcore.connection - DEBUG - atrace:87 - close.started
2025-07-15 16:24:17 - httpcore.connection - DEBUG - atrace:87 - close.complete
2025-07-15 16:24:18 - uvicorn.error - ERROR - send:134 - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\shutil.py", line 847, in move
    os.rename(src, real_dst)
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\blockbuster\blockbuster.py", line 106, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
FileExistsError: [WinError 183] Cannot create a file when that file already exists: '.langgraph_api\\.langgraph_checkpoint.3.pckl.tmp' -> '.langgraph_api\\.langgraph_checkpoint.3.pckl'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 595, in __exit__
    if cb(*exc_details):
       ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph\checkpoint\memory\__init__.py", line 578, in __exit__
    self.close()
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph\checkpoint\memory\__init__.py", line 571, in close
    self.sync()
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph\checkpoint\memory\__init__.py", line 566, in sync
    shutil.move(tempname, self.filename)  # atomic commit
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\shutil.py", line 868, in move
    os.unlink(src)
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\blockbuster\blockbuster.py", line 109, in wrapper
    raise BlockingError(func_name)
blockbuster.blockbuster.BlockingError: Blocking call to os.unlink

Heads up! LangGraph dev identified a synchronous blocking call in your code. When running in an ASGI web server, blocking calls can degrade performance for everyone since they tie up the event loop.

Here are your options to fix this:

1. Best approach: Convert any blocking code to use async/await patterns
   For example, use 'await aiohttp.get()' instead of 'requests.get()'

2. Quick fix: Move blocking operations to a separate thread
   Example: 'await asyncio.to_thread(your_blocking_function)'

3. Override (if you can't change the code):
   - For development: Run 'langgraph dev --allow-blocking'
   - For deployment: Set 'BG_JOB_ISOLATED_LOOPS=true' environment variable

These blocking operations can prevent health checks and slow down other runs in your deployment. Following these recommendations will help keep your LangGraph application running smoothly!

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\shutil.py", line 847, in move
    os.rename(src, real_dst)
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\blockbuster\blockbuster.py", line 106, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
FileExistsError: [WinError 183] Cannot create a file when that file already exists: '.langgraph_api\\.langgraph_checkpoint.2.pckl.tmp' -> '.langgraph_api\\.langgraph_checkpoint.2.pckl'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 595, in __exit__
    if cb(*exc_details):
       ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph\checkpoint\memory\__init__.py", line 578, in __exit__
    self.close()
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph\checkpoint\memory\__init__.py", line 571, in close
    self.sync()
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph\checkpoint\memory\__init__.py", line 566, in sync
    shutil.move(tempname, self.filename)  # atomic commit
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\shutil.py", line 868, in move
    os.unlink(src)
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\blockbuster\blockbuster.py", line 109, in wrapper
    raise BlockingError(func_name)
blockbuster.blockbuster.BlockingError: Blocking call to os.unlink

Heads up! LangGraph dev identified a synchronous blocking call in your code. When running in an ASGI web server, blocking calls can degrade performance for everyone since they tie up the event loop.

Here are your options to fix this:

1. Best approach: Convert any blocking code to use async/await patterns
   For example, use 'await aiohttp.get()' instead of 'requests.get()'

2. Quick fix: Move blocking operations to a separate thread
   Example: 'await asyncio.to_thread(your_blocking_function)'

3. Override (if you can't change the code):
   - For development: Run 'langgraph dev --allow-blocking'
   - For deployment: Set 'BG_JOB_ISOLATED_LOOPS=true' environment variable

These blocking operations can prevent health checks and slow down other runs in your deployment. Following these recommendations will help keep your LangGraph application running smoothly!

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\shutil.py", line 847, in move
    os.rename(src, real_dst)
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\blockbuster\blockbuster.py", line 106, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
FileExistsError: [WinError 183] Cannot create a file when that file already exists: '.langgraph_api\\.langgraph_checkpoint.1.pckl.tmp' -> '.langgraph_api\\.langgraph_checkpoint.1.pckl'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\starlette\routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 231, in __aexit__
    await self.gen.athrow(value)
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph_runtime_inmem\lifespan.py", line 74, in lifespan
    await stop_pool()
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph_runtime_inmem\database.py", line 191, in stop_pool
    async with Checkpointer():
               ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph\checkpoint\memory\__init__.py", line 118, in __aexit__
    return self.stack.__exit__(__exc_type, __exc_value, __traceback)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 610, in __exit__
    raise exc_details[1]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 595, in __exit__
    if cb(*exc_details):
       ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph\checkpoint\memory\__init__.py", line 578, in __exit__
    self.close()
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph\checkpoint\memory\__init__.py", line 571, in close
    self.sync()
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph\checkpoint\memory\__init__.py", line 566, in sync
    shutil.move(tempname, self.filename)  # atomic commit
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\shutil.py", line 868, in move
    os.unlink(src)
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\blockbuster\blockbuster.py", line 109, in wrapper
    raise BlockingError(func_name)
blockbuster.blockbuster.BlockingError: Blocking call to os.unlink

Heads up! LangGraph dev identified a synchronous blocking call in your code. When running in an ASGI web server, blocking calls can degrade performance for everyone since they tie up the event loop.

Here are your options to fix this:

1. Best approach: Convert any blocking code to use async/await patterns
   For example, use 'await aiohttp.get()' instead of 'requests.get()'

2. Quick fix: Move blocking operations to a separate thread
   Example: 'await asyncio.to_thread(your_blocking_function)'

3. Override (if you can't change the code):
   - For development: Run 'langgraph dev --allow-blocking'
   - For deployment: Set 'BG_JOB_ISOLATED_LOOPS=true' environment variable

These blocking operations can prevent health checks and slow down other runs in your deployment. Following these recommendations will help keep your LangGraph application running smoothly!

