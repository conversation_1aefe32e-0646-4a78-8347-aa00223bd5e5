2025-07-15 16:17:20 - outbond.logging_config - INFO - setup_logging:77 - Logging configured - File: logs\plan_node_20250715_161720.log, Level: DEBUG
2025-07-15 16:17:20 - outbond.logging_config - INFO - setup_logging:78 - Log rotation: 10MB max, 5 backups
2025-07-15 16:17:20 - outbond.logging_config - INFO - setup_plan_node_logging:110 - Plan node logging configured with detailed debugging enabled
2025-07-15 16:17:21 - langchain_community.utils.user_agent - WARNING - get_user_agent:11 - USER_AGENT environment variable not set, consider setting it to identify your requests.
2025-07-15 16:17:21 - langgraph_api.graph - INFO - register_graph:56 - {'graph_id': 'outbound', 'event': "Registering graph with id 'outbound'", 'thread_name': 'asyncio_1', 'logger': 'langgraph_api.graph', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:17:21.062380Z'}
2025-07-15 16:17:21 - langgraph_api.graph - INFO - register_graph:56 - {'graph_id': 'outbound2', 'event': "Registering graph with id 'outbound2'", 'thread_name': 'asyncio_2', 'logger': 'langgraph_api.graph', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:17:21.267641Z'}
2025-07-15 16:17:21 - langgraph_runtime_inmem.queue - INFO - queue:72 - {'event': 'Starting 1 background workers', 'thread_name': 'asyncio_1', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:17:21.465857Z'}
2025-07-15 16:17:21 - langgraph_runtime_inmem.queue - INFO - queue:89 - {'max': 1, 'available': 1, 'active': 0, 'event': 'Worker stats', 'thread_name': 'asyncio_2', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:17:21.466393Z'}
2025-07-15 16:17:21 - langgraph_runtime_inmem.queue - INFO - queue:127 - {'n_pending': 0, 'max_age_secs': None, 'med_age_secs': None, 'n_running': 0, 'event': 'Queue stats', 'thread_name': 'asyncio_1', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:17:21.965767Z'}
2025-07-15 16:17:21 - langgraph_runtime_inmem.queue - INFO - queue:132 - {'run_ids': [], 'event': 'Swept runs', 'thread_name': 'MainThread', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:17:21.965767Z'}
2025-07-15 16:17:26 - langgraph_api.auth.custom - INFO - get_auth_instance:61 - {'langgraph_auth': 'None', 'event': 'Getting auth instance: None', 'thread_name': 'MainThread', 'logger': 'langgraph_api.auth.custom', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:17:26.258832Z', 'path': '/assistants/search', 'method': 'POST'}
2025-07-15 16:17:55 - langgraph_runtime_inmem.ops - INFO - create_valid_run:358 - {'run_id': '1f061868-0517-639e-8662-c9018a499587', 'thread_id': '45bbba7b-8a9e-4029-83f9-e00799e1884b', 'assistant_id': 'ef8f2727-fb71-5e4d-8c6d-bddf52eab0d2', 'multitask_strategy': 'rollback', 'stream_mode': ['debug', 'messages'], 'temporary': False, 'after_seconds': 0, 'if_not_exists': 'reject', 'run_create_ms': 1, 'run_put_ms': 0, 'event': 'Created run', 'thread_name': 'MainThread', 'logger': 'langgraph_runtime_inmem.ops', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:17:55.481897Z', 'request_id': '2d3336bb-20f5-444d-817a-17d20978042e', 'path': '/threads/{thread_id}/runs/stream', 'method': 'POST'}
2025-07-15 16:17:56 - langgraph_api.worker - INFO - worker:110 - {'run_started_at': '2025-07-15T14:17:56.853334+00:00', 'run_creation_ms': 1, 'run_queue_ms': 1371, 'run_stream_start_ms': 0, 'event': 'Starting background run', 'thread_name': 'asyncio_1', 'logger': 'langgraph_api.worker', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:17:56.853334Z', 'run_id': '1f061868-0517-639e-8662-c9018a499587', 'run_attempt': 1, 'thread_id': '45bbba7b-8a9e-4029-83f9-e00799e1884b', 'assistant_id': 'ef8f2727-fb71-5e4d-8c6d-bddf52eab0d2', 'graph_id': 'outbound2', 'request_id': '2d3336bb-20f5-444d-817a-17d20978042e'}
2025-07-15 16:17:56 - langgraph_runtime_inmem.ops - DEBUG - join:2209 - {'run_id': '1f061868-0517-639e-8662-c9018a499587', 'stream_mode': 'metadata', 'message_id': '0', 'data': '{"run_id":"1f061868-0517-639e-8662-c9018a499587","attempt":1}', 'event': 'Streamed run event', 'thread_name': 'MainThread', 'logger': 'langgraph_runtime_inmem.ops', 'level': 'debug', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:17:56.855334Z', 'request_id': '2d3336bb-20f5-444d-817a-17d20978042e', 'path': '/threads/{thread_id}/runs/stream', 'method': 'POST'}
2025-07-15 16:17:56 - urllib3.connectionpool - DEBUG - _new_conn:1049 - Starting new HTTPS connection (1): api.smith.langchain.com:443
2025-07-15 16:17:57 - langgraph_runtime_inmem.ops - DEBUG - join:2209 - {'run_id': '1f061868-0517-639e-8662-c9018a499587', 'stream_mode': 'debug', 'message_id': '1', 'data': '{"step":-1,"timestamp":"2025-07-15T14:17:57.092704+00:00","type":"checkpoint","payload":{"config":{"configurable":{"checkpoint_ns":"","model":"anthropic/claude-3-5-sonnet-********","prompt":"You are an expert Sales Development Representative (SDR) research assistant. Your role is to conduct thorough prospect and account research to help SDRs build highly effective, personalized outreach campaigns.\\n\\nYou are researching the following target information for sales prospecting:\\n\\n<info>\\n{info}\\n</info>\\n\\nFocus your research on gathering actionable sales intelligence that will help with:\\n- Lead qualification and prioritization\\n- Personalized outreach messaging\\n- Identifying pain points and business challenges\\n- Finding recent company news, events, or triggers for timely outreach\\n- Understanding company structure, decision makers, and buying process\\n- Competitive landscape and positioning opportunities\\n- Technology stack and potential integration opportunities\\n\\nYou have access to the following research tools:\\n\\n- `Search`: Find recent news, company information, industry insights, and competitive intelligence\\n- `ScrapeWebsite`: Extract detailed information from company websites, press releases, case studies, and relevant pages\\n- `Info`: Submit your final research findings in a format that\'s immediately actionable for SDR outreach\\n\\nIMPORTANT EXTRACTION GUIDELINES:\\n- Always prioritize information that directly supports sales activities and provides clear value for prospect engagement\\n- For JSON format responses: If any data field cannot be fulfilled due to missing information, return null for that specific field\\n- Do not invent or guess data - use null when information is unavailable, unclear, or cannot be verified\\n- Ensure all available data is accurate and properly sourced\\n\\nResearch Target: {topic}","max_search_results":10,"max_info_tool_calls":3,"max_loops":6,"thread_id":"45bbba7b-8a9e-4029-83f9-e00799e1884b","x-auth-scheme":"langsmith","x-user-id":"0023af3f-9340-4ac5-8f69-92286eff8981","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36","x-request-id":"2d3336bb-20f5-444d-817a-17d20978042e","langgraph_auth_user":null,"langgraph_auth_user_id":"","langgraph_auth_permissions":[],"langgraph_request_id":"2d3336bb-20f5-444d-817a-17d20978042e","__request_start_time_ms__":*************,"__after_seconds__":0,"run_id":"1f061868-0517-639e-8662-c9018a499587","graph_id":"outbound2","assistant_id":"ef8f2727-fb71-5e4d-8c6d-bddf52eab0d2","user_id":"","checkpoint_id":"1f061868-1470-62f7-bfff-f7b366b42613"}},"parent_config":null,"values":{"messages":[],"loop_step":0,"citations":[]},"metadata":{"source":"input","step":-1,"parents":{}},"next":["__start__"],"tasks":[{"id":"6d5566a9-1943-19ad-7603-c4bb30cdc2d2","name":"__start__","interrupts":[],"state":null}],"checkpoint":{"checkpoint_id":"1f061868-1470-62f7-bfff-f7b366b42613","thread_id":"45bbba7b-8a9e-4029-83f9-e00799e1884b","checkpoint_ns":""},"parent_checkpoint":null}}', 'event': 'Streamed run event', 'thread_name': 'MainThread', 'logger': 'langgraph_runtime_inmem.ops', 'level': 'debug', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:17:57.093712Z', 'request_id': '2d3336bb-20f5-444d-817a-17d20978042e', 'path': '/threads/{thread_id}/runs/stream', 'method': 'POST'}
2025-07-15 16:17:57 - urllib3.connectionpool - DEBUG - _make_request:544 - https://api.smith.langchain.com:443 "GET /info HTTP/1.1" 200 749
2025-07-15 16:17:57 - langgraph_runtime_inmem.ops - DEBUG - join:2209 - {'run_id': '1f061868-0517-639e-8662-c9018a499587', 'stream_mode': 'debug', 'message_id': '2', 'data': '{"step":0,"timestamp":"2025-07-15T14:17:57.096711+00:00","type":"checkpoint","payload":{"config":{"configurable":{"checkpoint_ns":"","model":"anthropic/claude-3-5-sonnet-********","prompt":"You are an expert Sales Development Representative (SDR) research assistant. Your role is to conduct thorough prospect and account research to help SDRs build highly effective, personalized outreach campaigns.\\n\\nYou are researching the following target information for sales prospecting:\\n\\n<info>\\n{info}\\n</info>\\n\\nFocus your research on gathering actionable sales intelligence that will help with:\\n- Lead qualification and prioritization\\n- Personalized outreach messaging\\n- Identifying pain points and business challenges\\n- Finding recent company news, events, or triggers for timely outreach\\n- Understanding company structure, decision makers, and buying process\\n- Competitive landscape and positioning opportunities\\n- Technology stack and potential integration opportunities\\n\\nYou have access to the following research tools:\\n\\n- `Search`: Find recent news, company information, industry insights, and competitive intelligence\\n- `ScrapeWebsite`: Extract detailed information from company websites, press releases, case studies, and relevant pages\\n- `Info`: Submit your final research findings in a format that\'s immediately actionable for SDR outreach\\n\\nIMPORTANT EXTRACTION GUIDELINES:\\n- Always prioritize information that directly supports sales activities and provides clear value for prospect engagement\\n- For JSON format responses: If any data field cannot be fulfilled due to missing information, return null for that specific field\\n- Do not invent or guess data - use null when information is unavailable, unclear, or cannot be verified\\n- Ensure all available data is accurate and properly sourced\\n\\nResearch Target: {topic}","max_search_results":10,"max_info_tool_calls":3,"max_loops":6,"thread_id":"45bbba7b-8a9e-4029-83f9-e00799e1884b","x-auth-scheme":"langsmith","x-user-id":"0023af3f-9340-4ac5-8f69-92286eff8981","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36","x-request-id":"2d3336bb-20f5-444d-817a-17d20978042e","langgraph_auth_user":null,"langgraph_auth_user_id":"","langgraph_auth_permissions":[],"langgraph_request_id":"2d3336bb-20f5-444d-817a-17d20978042e","__request_start_time_ms__":*************,"__after_seconds__":0,"run_id":"1f061868-0517-639e-8662-c9018a499587","graph_id":"outbound2","assistant_id":"ef8f2727-fb71-5e4d-8c6d-bddf52eab0d2","user_id":"","checkpoint_id":"1f061868-147b-6339-8000-1ddb2357500a"}},"parent_config":{"configurable":{"checkpoint_ns":"","model":"anthropic/claude-3-5-sonnet-********","prompt":"You are an expert Sales Development Representative (SDR) research assistant. Your role is to conduct thorough prospect and account research to help SDRs build highly effective, personalized outreach campaigns.\\n\\nYou are researching the following target information for sales prospecting:\\n\\n<info>\\n{info}\\n</info>\\n\\nFocus your research on gathering actionable sales intelligence that will help with:\\n- Lead qualification and prioritization\\n- Personalized outreach messaging\\n- Identifying pain points and business challenges\\n- Finding recent company news, events, or triggers for timely outreach\\n- Understanding company structure, decision makers, and buying process\\n- Competitive landscape and positioning opportunities\\n- Technology stack and potential integration opportunities\\n\\nYou have access to the following research tools:\\n\\n- `Search`: Find recent news, company information, industry insights, and competitive intelligence\\n- `ScrapeWebsite`: Extract detailed information from company websites, press releases, case studies, and relevant pages\\n- `Info`: Submit your final research findings in a format that\'s immediately actionable for SDR outreach\\n\\nIMPORTANT EXTRACTION GUIDELINES:\\n- Always prioritize information that directly supports sales activities and provides clear value for prospect engagement\\n- For JSON format responses: If any data field cannot be fulfilled due to missing information, return null for that specific field\\n- Do not invent or guess data - use null when information is unavailable, unclear, or cannot be verified\\n- Ensure all available data is accurate and properly sourced\\n\\nResearch Target: {topic}","max_search_results":10,"max_info_tool_calls":3,"max_loops":6,"thread_id":"45bbba7b-8a9e-4029-83f9-e00799e1884b","x-auth-scheme":"langsmith","x-user-id":"0023af3f-9340-4ac5-8f69-92286eff8981","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36","x-request-id":"2d3336bb-20f5-444d-817a-17d20978042e","langgraph_auth_user":null,"langgraph_auth_user_id":"","langgraph_auth_permissions":[],"langgraph_request_id":"2d3336bb-20f5-444d-817a-17d20978042e","__request_start_time_ms__":*************,"__after_seconds__":0,"run_id":"1f061868-0517-639e-8662-c9018a499587","graph_id":"outbound2","assistant_id":"ef8f2727-fb71-5e4d-8c6d-bddf52eab0d2","user_id":"","checkpoint_id":"1f061868-1470-62f7-bfff-f7b366b42613"}},"values":{"topic":"Give me a short company summary for Stripe.","extraction_schema":{"format":"json","fields":{"company_name":"string","industry":"string","hq_location":"string","short_description":"string"}},"messages":[],"loop_step":0,"citations":[]},"metadata":{"source":"loop","step":0,"parents":{}},"next":["sdr_research_orchestrator"],"tasks":[{"id":"029d3f3f-72bb-b2d9-04c0-f14fa2f47f87","name":"sdr_research_orchestrator","interrupts":[],"state":null}],"checkpoint":{"checkpoint_id":"1f061868-147b-6339-8000-1ddb2357500a","thread_id":"45bbba7b-8a9e-4029-83f9-e00799e1884b","checkpoint_ns":""},"parent_checkpoint":{"checkpoint_id":"1f061868-1470-62f7-bfff-f7b366b42613","thread_id":"45bbba7b-8a9e-4029-83f9-e00799e1884b","checkpoint_ns":""}}}', 'event': 'Streamed run event', 'thread_name': 'MainThread', 'logger': 'langgraph_runtime_inmem.ops', 'level': 'debug', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:17:57.097711Z', 'request_id': '2d3336bb-20f5-444d-817a-17d20978042e', 'path': '/threads/{thread_id}/runs/stream', 'method': 'POST'}
2025-07-15 16:17:57 - langsmith.client - DEBUG - tracing_control_thread_func_compress_parallel:594 - Tracing control thread func compress parallel called
2025-07-15 16:17:57 - langgraph_runtime_inmem.ops - DEBUG - join:2209 - {'run_id': '1f061868-0517-639e-8662-c9018a499587', 'stream_mode': 'debug', 'message_id': '3', 'data': '{"step":1,"timestamp":"2025-07-15T14:17:57.096711+00:00","type":"task","payload":{"id":"029d3f3f-72bb-b2d9-04c0-f14fa2f47f87","name":"sdr_research_orchestrator","input":{"topic":"Give me a short company summary for Stripe.","extraction_schema":{"format":"json","fields":{"company_name":"string","industry":"string","hq_location":"string","short_description":"string"}},"info":null,"messages":[],"loop_step":0,"citations":[]},"triggers":["branch:to:sdr_research_orchestrator"]}}', 'event': 'Streamed run event', 'thread_name': 'MainThread', 'logger': 'langgraph_runtime_inmem.ops', 'level': 'debug', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:17:57.099710Z', 'request_id': '2d3336bb-20f5-444d-817a-17d20978042e', 'path': '/threads/{thread_id}/runs/stream', 'method': 'POST'}
2025-07-15 16:17:57 - langsmith.client - DEBUG - _send_multipart_req:1957 - Sending multipart request with context: trace=1f061868-0517-639e-8662-c9018a499587,id=1f061868-0517-639e-8662-c9018a499587
2025-07-15 16:17:57 - urllib3.connectionpool - DEBUG - _make_request:544 - https://api.smith.langchain.com:443 "POST /runs/multipart HTTP/1.1" 202 34
2025-07-15 16:17:57 - anthropic._base_client - DEBUG - _build_request:493 - Request options: {'method': 'post', 'url': '/v1/messages', 'files': None, 'idempotency_key': 'stainless-python-retry-ab133fbd-15ae-436d-821d-974b2e10e587', 'json_data': {'max_tokens': 1024, 'messages': [{'role': 'user', 'content': 'You are an expert Sales Development Representative (SDR) research assistant. Your role is to conduct thorough prospect and account research to help SDRs build highly effective, personalized outreach campaigns.\n\nYou are researching the following target information for sales prospecting:\n\n<info>\n{\n  "type": "object",\n  "properties": {\n    "company_name": {\n      "type": [\n        "string",\n        "null"\n      ],\n      "description": "Field of type string. Use null if data cannot be found or fulfilled."\n    },\n    "industry": {\n      "type": [\n        "string",\n        "null"\n      ],\n      "description": "Field of type string. Use null if data cannot be found or fulfilled."\n    },\n    "hq_location": {\n      "type": [\n        "string",\n        "null"\n      ],\n      "description": "Field of type string. Use null if data cannot be found or fulfilled."\n    },\n    "short_description": {\n      "type": [\n        "string",\n        "null"\n      ],\n      "description": "Field of type string. Use null if data cannot be found or fulfilled."\n    }\n  },\n  "additionalProperties": false\n}\n</info>\n\nFocus your research on gathering actionable sales intelligence that will help with:\n- Lead qualification and prioritization\n- Personalized outreach messaging\n- Identifying pain points and business challenges\n- Finding recent company news, events, or triggers for timely outreach\n- Understanding company structure, decision makers, and buying process\n- Competitive landscape and positioning opportunities\n- Technology stack and potential integration opportunities\n\nYou have access to the following research tools:\n\n- `Search`: Find recent news, company information, industry insights, and competitive intelligence\n- `ScrapeWebsite`: Extract detailed information from company websites, press releases, case studies, and relevant pages\n- `Info`: Submit your final research findings in a format that\'s immediately actionable for SDR outreach\n\nIMPORTANT EXTRACTION GUIDELINES:\n- Always prioritize information that directly supports sales activities and provides clear value for prospect engagement\n- For JSON format responses: If any data field cannot be fulfilled due to missing information, return null for that specific field\n- Do not invent or guess data - use null when information is unavailable, unclear, or cannot be verified\n- Ensure all available data is accurate and properly sourced\n\nResearch Target: Give me a short company summary for Stripe.'}], 'model': 'claude-3-5-sonnet-********', 'stream': True, 'tool_choice': {'type': 'any'}, 'tools': [{'name': 'scrape_website', 'input_schema': {'properties': {'url': {'type': 'string'}}, 'required': ['url'], 'type': 'object'}, 'description': 'Scrape and summarize content from a given URL. Now includes citation tracking - the scraped website is automatically added to state citations.'}, {'name': 'search', 'input_schema': {'properties': {'query': {'type': 'string'}}, 'required': ['query'], 'type': 'object'}, 'description': "Query a search engine. This function queries the web to fetch comprehensive, accurate, and trusted results. It's particularly useful\nfor answering questions about current events. Provide as much context in the query as needed to ensure high recall. Now includes citation tracking - all search results are automatically added to the state citations."}, {'name': 'Info', 'input_schema': {'type': 'object', 'properties': {'company_name': {'type': ['string', 'null'], 'description': 'Field of type string. Use null if data cannot be found or fulfilled.'}, 'industry': {'type': ['string', 'null'], 'description': 'Field of type string. Use null if data cannot be found or fulfilled.'}, 'hq_location': {'type': ['string', 'null'], 'description': 'Field of type string. Use null if data cannot be found or fulfilled.'}, 'short_description': {'type': ['string', 'null'], 'description': 'Field of type string. Use null if data cannot be found or fulfilled.'}}, 'additionalProperties': False}, 'description': 'Call this when you have gathered all the relevant info. Use null for any fields where data cannot be found or fulfilled. Citations will be automatically added to the structured output.'}]}}
2025-07-15 16:17:57 - langsmith.client - DEBUG - _send_compressed_multipart_req:2025 - Sending compressed multipart request with context: trace=1f061868-0517-639e-8662-c9018a499587,id=17cdd397-622e-4c0c-8a46-8474cc5f3906; trace=1f061868-0517-639e-8662-c9018a499587,id=f1b779c9-ca93-420b-bca2-d2e131701d31
2025-07-15 16:17:57 - langgraph_runtime_inmem.ops - DEBUG - join:2209 - {'run_id': '1f061868-0517-639e-8662-c9018a499587', 'stream_mode': 'debug', 'message_id': '4', 'data': '{"step":1,"timestamp":"2025-07-15T14:17:57.706177+00:00","type":"task_result","payload":{"id":"029d3f3f-72bb-b2d9-04c0-f14fa2f47f87","name":"sdr_research_orchestrator","error":{"error":"TypeError","message":"\\"Could not resolve authentication method. Expected either api_key or auth_token to be set. Or for one of the `X-Api-Key` or `Authorization` headers to be explicitly omitted\\""},"result":[],"interrupts":[]}}', 'event': 'Streamed run event', 'thread_name': 'MainThread', 'logger': 'langgraph_runtime_inmem.ops', 'level': 'debug', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:17:57.707222Z', 'request_id': '2d3336bb-20f5-444d-817a-17d20978042e', 'path': '/threads/{thread_id}/runs/stream', 'method': 'POST'}
2025-07-15 16:17:57 - langgraph_runtime_inmem.ops - DEBUG - join:2209 - {'run_id': '1f061868-0517-639e-8662-c9018a499587', 'stream_mode': 'error', 'message_id': '5', 'data': '{"error":"TypeError","message":"\\"Could not resolve authentication method. Expected either api_key or auth_token to be set. Or for one of the `X-Api-Key` or `Authorization` headers to be explicitly omitted\\""}', 'event': 'Streamed run event', 'thread_name': 'MainThread', 'logger': 'langgraph_runtime_inmem.ops', 'level': 'debug', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:17:57.758033Z', 'request_id': '2d3336bb-20f5-444d-817a-17d20978042e', 'path': '/threads/{thread_id}/runs/stream', 'method': 'POST'}
2025-07-15 16:17:57 - langgraph_api.worker - ERROR - wrap_user_errors:135 - {'event': 'Run encountered an error in graph: <class \'TypeError\'>("Could not resolve authentication method. Expected either api_key or auth_token to be set. Or for one of the `X-Api-Key` or `Authorization` headers to be explicitly omitted")', 'thread_name': 'MainThread', 'logger': 'langgraph_api.worker', 'level': 'error', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:17:57.759035Z', 'exception': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\langgraph_api\\worker.py", line 132, in wrap_user_errors\n    await consume(stream, run_id, resumable)\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\langgraph_api\\stream.py", line 332, in consume\n    raise e\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\langgraph_api\\stream.py", line 316, in consume\n    async for mode, payload in stream:\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\langgraph_api\\stream.py", line 251, in astream_state\n    event = await wait_if_not_done(anext(stream, sentinel), done)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\langgraph_api\\asyncio.py", line 91, in wait_if_not_done\n    raise e.exceptions[0] from None\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\langgraph\\pregel\\__init__.py", line 2768, in astream\n    async for _ in runner.atick(\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\langgraph\\pregel\\runner.py", line 401, in atick\n    _panic_or_proceed(\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\langgraph\\pregel\\runner.py", line 511, in _panic_or_proceed\n    raise exc\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\langgraph\\pregel\\retry.py", line 137, in arun_with_retry\n    return await task.proc.ainvoke(task.input, config)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\langgraph\\utils\\runnable.py", line 672, in ainvoke\n    input = await asyncio.create_task(\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\langgraph\\utils\\runnable.py", line 440, in ainvoke\n    ret = await self.afunc(*args, **kwargs)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Dev_Lasngrapg\\assesment\\src\\outbond2\\nodes\\sdr_research_orchestrator.py", line 83, in sdr_research_orchestrator\n    response = cast(AIMessage, await model.ainvoke(messages))\n                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\langchain_core\\runnables\\base.py", line 5444, in ainvoke\n    return await self.bound.ainvoke(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py", line 400, in ainvoke\n    llm_result = await self.agenerate_prompt(\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py", line 974, in agenerate_prompt\n    return await self.agenerate(\n           ^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py", line 932, in agenerate\n    raise exceptions[0]\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py", line 1089, in _agenerate_with_cache\n    async for chunk in self._astream(messages, stop=stop, **kwargs):\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\langchain_anthropic\\chat_models.py", line 1422, in _astream\n    stream = await self._acreate(payload)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\langchain_anthropic\\chat_models.py", line 1370, in _acreate\n    return await self._async_client.messages.create(**payload)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\anthropic\\resources\\messages\\messages.py", line 2272, in create\n    return await self._post(\n           ^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\anthropic\\_base_client.py", line 1888, in post\n    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\anthropic\\_base_client.py", line 1609, in request\n    request = self._build_request(options, retries_taken=retries_taken)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\anthropic\\_base_client.py", line 506, in _build_request\n    headers = self._build_headers(options, retries_taken=retries_taken)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\anthropic\\_base_client.py", line 447, in _build_headers\n    self._validate_headers(headers_dict, custom_headers)\n  File "C:\\Users\\<USER>\\AppData\\Local\\uv\\cache\\archive-v0\\6YF11-yvVix5nRhxaI3Nd\\Lib\\site-packages\\anthropic\\_client.py", line 436, in _validate_headers\n    raise TypeError(\nTypeError: "Could not resolve authentication method. Expected either api_key or auth_token to be set. Or for one of the `X-Api-Key` or `Authorization` headers to be explicitly omitted"\nDuring task with name \'sdr_research_orchestrator\' and id \'029d3f3f-72bb-b2d9-04c0-f14fa2f47f87\'', 'run_id': '1f061868-0517-639e-8662-c9018a499587', 'run_attempt': 1, 'thread_id': '45bbba7b-8a9e-4029-83f9-e00799e1884b', 'assistant_id': 'ef8f2727-fb71-5e4d-8c6d-bddf52eab0d2', 'graph_id': 'outbound2', 'request_id': '2d3336bb-20f5-444d-817a-17d20978042e'}
2025-07-15 16:17:57 - langgraph_api.worker - ERROR - worker:301 - {'run_id': '1f061868-0517-639e-8662-c9018a499587', 'run_attempt': 1, 'run_created_at': '2025-07-15T14:17:55.481897+00:00', 'run_started_at': '2025-07-15T14:17:56.853334+00:00', 'run_ended_at': '2025-07-15T14:17:57.804008+00:00', 'run_exec_ms': 950, 'run_completed_in_ms': 2324, 'event': 'Background run failed. Exception: <class \'TypeError\'>("Could not resolve authentication method. Expected either api_key or auth_token to be set. Or for one of the `X-Api-Key` or `Authorization` headers to be explicitly omitted")', 'thread_name': 'asyncio_2', 'logger': 'langgraph_api.worker', 'level': 'error', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:17:57.804008Z', 'thread_id': '45bbba7b-8a9e-4029-83f9-e00799e1884b', 'assistant_id': 'ef8f2727-fb71-5e4d-8c6d-bddf52eab0d2', 'graph_id': 'outbound2', 'request_id': '2d3336bb-20f5-444d-817a-17d20978042e'}
NoneType: None
2025-07-15 16:17:57 - urllib3.connectionpool - DEBUG - _make_request:544 - https://api.smith.langchain.com:443 "POST /runs/multipart HTTP/1.1" 202 34
2025-07-15 16:17:58 - langsmith.client - DEBUG - _send_compressed_multipart_req:2025 - Sending compressed multipart request with context: trace=1f061868-0517-639e-8662-c9018a499587,id=f1b779c9-ca93-420b-bca2-d2e131701d31; trace=1f061868-0517-639e-8662-c9018a499587,id=17cdd397-622e-4c0c-8a46-8474cc5f3906; trace=1f061868-0517-639e-8662-c9018a499587,id=1f061868-0517-639e-8662-c9018a499587
2025-07-15 16:17:58 - urllib3.connectionpool - DEBUG - _make_request:544 - https://api.smith.langchain.com:443 "POST /runs/multipart HTTP/1.1" 202 34
2025-07-15 16:18:21 - langgraph_runtime_inmem.queue - INFO - queue:89 - {'max': 1, 'available': 1, 'active': 0, 'event': 'Worker stats', 'thread_name': 'asyncio_1', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:18:21.626016Z'}
2025-07-15 16:18:22 - langgraph_runtime_inmem.queue - INFO - queue:127 - {'n_pending': 0, 'max_age_secs': None, 'med_age_secs': None, 'n_running': 0, 'event': 'Queue stats', 'thread_name': 'asyncio_2', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:18:22.128583Z'}
2025-07-15 16:19:22 - langgraph_runtime_inmem.queue - INFO - queue:89 - {'max': 1, 'available': 1, 'active': 0, 'event': 'Worker stats', 'thread_name': 'asyncio_4', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:19:22.125397Z'}
2025-07-15 16:19:22 - langgraph_runtime_inmem.queue - INFO - queue:127 - {'n_pending': 0, 'max_age_secs': None, 'med_age_secs': None, 'n_running': 0, 'event': 'Queue stats', 'thread_name': 'asyncio_1', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:19:22.627539Z'}
2025-07-15 16:20:22 - langgraph_runtime_inmem.queue - INFO - queue:89 - {'max': 1, 'available': 1, 'active': 0, 'event': 'Worker stats', 'thread_name': 'asyncio_2', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:20:22.628152Z'}
2025-07-15 16:20:23 - langgraph_runtime_inmem.queue - INFO - queue:127 - {'n_pending': 0, 'max_age_secs': None, 'med_age_secs': None, 'n_running': 0, 'event': 'Queue stats', 'thread_name': 'asyncio_4', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:20:23.130319Z'}
2025-07-15 16:21:22 - langgraph_runtime_inmem.queue - INFO - queue:132 - {'run_ids': [], 'event': 'Swept runs', 'thread_name': 'MainThread', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:21:22.626990Z'}
2025-07-15 16:21:23 - langgraph_runtime_inmem.queue - INFO - queue:89 - {'max': 1, 'available': 1, 'active': 0, 'event': 'Worker stats', 'thread_name': 'asyncio_0', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:21:23.126024Z'}
2025-07-15 16:21:23 - langgraph_runtime_inmem.queue - INFO - queue:127 - {'n_pending': 0, 'max_age_secs': None, 'med_age_secs': None, 'n_running': 0, 'event': 'Queue stats', 'thread_name': 'asyncio_2', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:21:23.627446Z'}
2025-07-15 16:22:18 - httpcore.connection - DEBUG - atrace:87 - close.started
2025-07-15 16:22:18 - httpcore.connection - DEBUG - atrace:87 - close.complete
2025-07-15 16:22:18 - httpcore.connection - DEBUG - atrace:87 - connect_tcp.started host='api.smith.langchain.com' port=443 local_address=None timeout=5 socket_options=None
2025-07-15 16:22:18 - httpcore.connection - DEBUG - atrace:87 - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C5200B7260>
2025-07-15 16:22:18 - httpcore.connection - DEBUG - atrace:87 - start_tls.started ssl_context=<truststore._api.SSLContext object at 0x000001C51DB63A50> server_hostname='api.smith.langchain.com' timeout=5
2025-07-15 16:22:18 - httpcore.connection - DEBUG - atrace:87 - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001C523880AA0>
2025-07-15 16:22:18 - httpcore.http11 - DEBUG - atrace:87 - send_request_headers.started request=<Request [b'POST']>
2025-07-15 16:22:18 - httpcore.http11 - DEBUG - atrace:87 - send_request_headers.complete
2025-07-15 16:22:18 - httpcore.http11 - DEBUG - atrace:87 - send_request_body.started request=<Request [b'POST']>
2025-07-15 16:22:18 - httpcore.http11 - DEBUG - atrace:87 - send_request_body.complete
2025-07-15 16:22:18 - httpcore.http11 - DEBUG - atrace:87 - receive_response_headers.started request=<Request [b'POST']>
2025-07-15 16:22:19 - httpcore.http11 - DEBUG - atrace:87 - receive_response_headers.complete return_value=(b'HTTP/1.1', 204, b'No Content', [(b'Cache-Control', b'no-cache, no-store, no-transform, must-revalidate, private, max-age=0'), (b'Expires', b'Thu, 01 Jan 1970 00:00:00 GMT'), (b'Pragma', b'no-cache'), (b'Vary', b'Origin'), (b'X-Accel-Expires', b'0'), (b'X-Datadog-Trace-Id', b'********************************'), (b'Date', b'Tue, 15 Jul 2025 14:22:18 GMT'), (b'Via', b'1.1 google'), (b'Access-Control-Allow-Origin', b''), (b'Access-Control-Allow-Methods', b'*'), (b'Access-Control-Allow-Headers', b'*'), (b'Access-Control-Allow-Credentials', b'true'), (b'Access-Control-Expose-Headers', b'*'), (b'Access-Control-Max-Age', b'600'), (b'Content-Security-Policy', b"frame-ancestors 'self' https://smith.langchain.com; object-src 'none'"), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains; preload'), (b'Timing-Allow-Origin', b''), (b'X-Content-Type-Options', b'nosniff'), (b'Alt-Svc', b'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000')])
2025-07-15 16:22:19 - httpx - INFO - _send_single_request:1740 - HTTP Request: POST https://api.smith.langchain.com/v1/metadata/submit "HTTP/1.1 204 No Content"
2025-07-15 16:22:19 - httpcore.http11 - DEBUG - atrace:87 - receive_response_body.started request=<Request [b'POST']>
2025-07-15 16:22:19 - httpcore.http11 - DEBUG - atrace:87 - receive_response_body.complete
2025-07-15 16:22:19 - httpcore.http11 - DEBUG - atrace:87 - response_closed.started
2025-07-15 16:22:19 - httpcore.http11 - DEBUG - atrace:87 - response_closed.complete
2025-07-15 16:22:19 - langgraph_api.metadata - INFO - metadata_loop:177 - {'event': 'Successfully submitted metadata to LangSmith instance', 'thread_name': 'MainThread', 'logger': 'langgraph_api.metadata', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:22:19.108340Z'}
2025-07-15 16:22:23 - langgraph_runtime_inmem.queue - INFO - queue:89 - {'max': 1, 'available': 1, 'active': 0, 'event': 'Worker stats', 'thread_name': 'asyncio_0', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:22:23.626317Z'}
2025-07-15 16:22:24 - langgraph_runtime_inmem.queue - INFO - queue:127 - {'n_pending': 0, 'max_age_secs': None, 'med_age_secs': None, 'n_running': 0, 'event': 'Queue stats', 'thread_name': 'asyncio_2', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:22:24.127852Z'}
2025-07-15 16:23:02 - langgraph_runtime_inmem.queue - INFO - queue:141 - {'event': 'Shutting down background workers', 'thread_name': 'MainThread', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:23:02.032157Z'}
2025-07-15 16:23:02 - langgraph_api.graph - INFO - stop_remote_graphs:404 - {'event': 'Shutting down remote graphs', 'thread_name': 'MainThread', 'logger': 'langgraph_api.graph', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T14:23:02.032157Z'}
2025-07-15 16:23:02 - httpcore.connection - DEBUG - atrace:87 - close.started
2025-07-15 16:23:02 - httpcore.connection - DEBUG - atrace:87 - close.complete
2025-07-15 16:23:02 - langsmith.client - DEBUG - keep_thread_active:623 - Main thread is dead, stopping compression thread
2025-07-15 16:23:02 - langsmith.client - DEBUG - tracing_control_thread_func_compress_parallel:726 - Compressed traces control thread is shutting down
2025-07-15 16:23:02 - langsmith.client - DEBUG - keep_thread_active:512 - Main thread is dead, stopping tracing thread
2025-07-15 16:23:02 - langsmith.client - DEBUG - tracing_control_thread_func:585 - Tracing control thread is shutting down
2025-07-15 16:23:02 - langsmith.client - DEBUG - close_session:250 - Closing Client.session
2025-07-15 16:23:02 - langsmith.client - DEBUG - close_session:250 - Closing Client.session
