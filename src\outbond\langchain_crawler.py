"""LangChain-based web crawler as a free alternative to Firecrawl."""

import asyncio
import os
import re
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse

import aiohttp
from bs4 import BeautifulSoup
from langchain_community.document_loaders import WebB<PERSON>Loader
from langchain_community.tools import TavilySearchResults


class LangChainCrawler:
    """Free LangChain-based web crawler replacement for FirecrawlClient."""
    
    def __init__(self, provided_api_key: Optional[str] = None):
        """Initialize the LangChain crawler.
        
        Args:
            provided_api_key: Tavily API key for search functionality
            
        Raises:
            ValueError: If no Tavily API key is provided
        """
        tavily_api_key = provided_api_key or os.getenv('TAVILY_API_KEY')
        if not tavily_api_key:
            raise ValueError('TAVILY_API_KEY is required for search functionality')
        
        # Initialize Tavily search for web search functionality
        self.search_tool = TavilySearchResults(
            api_key=tavily_api_key,
            max_results=10,
            include_raw_content=True,
            include_images=False
        )
    
    def _html_to_markdown(self, html_content: str) -> str:
        """Convert HTML content to markdown format.
        
        Args:
            html_content: Raw HTML content
            
        Returns:
            Markdown formatted content
        """
        if not html_content:
            return ""
            
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.extract()
            
            # Get text content
            text = soup.get_text()
            
            # Clean up whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            # Basic markdown conversion for common elements
            # Convert headers
            for i in range(1, 7):
                h_tags = soup.find_all(f'h{i}')
                for h_tag in h_tags:
                    h_text = h_tag.get_text().strip()
                    if h_text:
                        text = text.replace(h_text, f"{'#' * i} {h_text}")
            
            # Convert links (basic)
            for link in soup.find_all('a', href=True):
                link_text = link.get_text().strip()
                href = link['href']
                if link_text and href:
                    text = text.replace(link_text, f"[{link_text}]({href})")
            
            return text
            
        except Exception:
            # Fallback to simple text extraction
            soup = BeautifulSoup(html_content, 'html.parser')
            return soup.get_text()
    
    async def scrape_url(self, url: str, timeout_ms: int = 15000) -> Dict[str, Any]:
        """Scrape a single URL using LangChain's WebBaseLoader.
        
        Args:
            url: The URL to scrape
            timeout_ms: Timeout in milliseconds
            
        Returns:
            Dictionary with scraped content and metadata
        """
        try:
            # Convert timeout from ms to seconds
            timeout_seconds = timeout_ms / 1000.0
            
            # Use WebBaseLoader for scraping
            loader = WebBaseLoader(
                web_paths=[url],
                requests_kwargs={"timeout": timeout_seconds}
            )
            
            # Load the document (synchronous)
            documents = loader.load()
            
            if not documents:
                return {
                    'markdown': '',
                    'html': '',
                    'metadata': {'error': 'No content found'},
                    'success': False,
                    'error': 'no_content',
                }
            
            doc = documents[0]
            content = doc.page_content
            metadata = doc.metadata
            
            # Get the actual HTML if possible
            html_content = ""
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout_seconds)) as session:
                    async with session.get(url) as response:
                        if response.status == 200:
                            html_content = await response.text()
            except Exception:
                pass  # HTML retrieval is optional
            
            # Convert to markdown format
            markdown_content = self._html_to_markdown(html_content) if html_content else content
            
            return {
                'markdown': markdown_content,
                'html': html_content,
                'metadata': {
                    **metadata,
                    'url': url,
                    'status': 'success'
                },
                'success': True,
            }
            
        except asyncio.TimeoutError:
            return {
                'markdown': '',
                'html': '',
                'metadata': {
                    'error': 'Request timed out',
                    'url': url,
                    'timeout': timeout_ms
                },
                'success': False,
                'error': 'timeout',
            }
            
        except Exception as error:
            error_message = str(error)
            
            # Handle 403 errors gracefully
            if '403' in error_message or 'Forbidden' in error_message:
                return {
                    'markdown': '',
                    'html': '',
                    'metadata': {
                        'error': 'This website blocks automated access',
                        'statusCode': 403,
                        'url': url
                    },
                    'success': False,
                    'error': 'forbidden',
                }
            
            # Return error info for other failures
            return {
                'markdown': '',
                'html': '',
                'metadata': {
                    'error': error_message,
                    'url': url
                },
                'success': False,
                'error': 'failed',
            }
    
    async def map_url(self, url: str, options: Optional[Dict[str, Union[str, int]]] = None) -> Dict[str, Any]:
        """Map a URL to discover links using basic web scraping.
        
        Args:
            url: The URL to map
            options: Optional parameters like search and limit
            
        Returns:
            Dictionary with discovered links and metadata
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status != 200:
                        raise Exception(f"HTTP {response.status}")
                    
                    html_content = await response.text()
                    soup = BeautifulSoup(html_content, 'html.parser')
                    
                    # Extract all links
                    links = []
                    base_domain = urlparse(url).netloc
                    
                    for link_tag in soup.find_all('a', href=True):
                        href = link_tag['href']
                        text = link_tag.get_text().strip()
                        
                        # Convert relative URLs to absolute
                        if href.startswith('/'):
                            href = f"https://{base_domain}{href}"
                        elif not href.startswith(('http://', 'https://')):
                            continue
                        
                        # Filter by search term if provided
                        if options and 'search' in options:
                            search_term = options['search'].lower()
                            if search_term not in text.lower() and search_term not in href.lower():
                                continue
                        
                        links.append({
                            'url': href,
                            'text': text
                        })
                    
                    # Apply limit if specified
                    limit = options.get('limit', 10) if options else 10
                    links = links[:limit]
                    
                    return {
                        'links': links,
                        'metadata': {
                            'url': url,
                            'total_links_found': len(links)
                        },
                    }
                    
        except Exception as error:
            raise Exception(f"Failed to map URL {url}: {str(error)}")
    
    async def search(self, query: str, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Search the web using Tavily and optionally scrape results.
        
        Args:
            query: The search query
            options: Optional parameters like limit and scrapeOptions
            
        Returns:
            Dictionary with search results and scraped content
        """
        try:
            # Extract parameters
            limit = options.get('limit', 10) if options else 10
            should_scrape = options.get('scrapeOptions', True) if options else True
            
            # Perform search using Tavily
            search_results = await self.search_tool.ainvoke({
                "query": query,
                "max_results": limit
            })

            # Type check: Tavily may return a string error message
            if not isinstance(search_results, list):
                raise Exception(f"TavilySearchResults returned error: {search_results}")
            
            # Transform results to match Firecrawl format
            enriched_data = []
            
            for item in search_results:
                url = item.get('url', '')
                title = item.get('title', 'Untitled')
                snippet = item.get('content', '')
                raw_content = item.get('raw_content', '')
                
                # Create base result
                result_item = {
                    'url': url,
                    'title': title,
                    'description': snippet,
                    'markdown': '',
                    'html': '',
                    'links': [],
                    'screenshot': None,
                    'metadata': {
                        'source': 'tavily',
                        'snippet': snippet
                    },
                    'scraped': False,
                    'content': snippet,  # Initial content from search
                }
                
                # Add favicon
                try:
                    domain = urlparse(url).hostname
                    result_item['favicon'] = f"https://{domain}/favicon.ico"
                    result_item['metadata']['favicon'] = result_item['favicon']
                except Exception:
                    result_item['favicon'] = None
                
                # Scrape full content if requested and raw_content is available
                if should_scrape and raw_content:
                    result_item['markdown'] = raw_content
                    result_item['content'] = raw_content
                    result_item['scraped'] = True
                elif should_scrape:
                    # Attempt to scrape the URL directly
                    try:
                        scraped_result = await self.scrape_url(url, timeout_ms=10000)
                        if scraped_result.get('success'):
                            result_item['markdown'] = scraped_result.get('markdown', '')
                            result_item['html'] = scraped_result.get('html', '')
                            result_item['content'] = scraped_result.get('markdown', snippet)
                            result_item['scraped'] = True
                        else:
                            # Use snippet as fallback
                            result_item['content'] = snippet
                    except Exception:
                        # Use snippet as fallback
                        result_item['content'] = snippet
                
                enriched_data.append(result_item)
            
            return {
                'data': enriched_data,
                'results': enriched_data,  # For backward compatibility
                'metadata': {
                    'query': query,
                    'total_results': len(enriched_data),
                    'source': 'tavily'
                },
            }
            
        except Exception as error:
            raise Exception(f"Search failed for query '{query}': {str(error)}") 