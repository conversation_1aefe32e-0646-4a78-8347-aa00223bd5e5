

from typing import TypedDict

from langgraph.graph import END, START, StateGraph
from outbond.configuration import Configuration
from outbond.nodes import analyzer_node, handle_error_node, plan_node, search_node, scrape_node, analyze_node, complete_node, summarize_node

from outbond.nodes.formatter import formatter_node
from outbond.state import SearchPhase, SearchState



workflow = StateGraph(SearchState, config_schema=Configuration)


workflow.add_node("handleError", handle_error_node)
workflow.add_node("analyzer", analyzer_node)
workflow.add_node("plan", plan_node)
workflow.add_node("search", search_node)
workflow.add_node("scrape", scrape_node)
workflow.add_node("analyze", analyze_node)
workflow.add_node("formatter", formatter_node)
workflow.add_node("complete", complete_node)
workflow.add_node("summarize", summarize_node)

workflow.add_edge(START, "analyzer") 

workflow.add_conditional_edges(
    "analyzer",
    lambda state: "handleError" if state.error else "plan",
     {
        "handleError": "handleError",
        "plan": "plan"
    }
)


# Add conditional edges from plan
workflow.add_conditional_edges(
    "plan",
    lambda state: "handleError" if state.phase == SearchPhase.ERROR else "search",
    {
        "handleError": "handleError",
        "search": "search"
    }
)
# Add conditional edges from search to handle sequential processing
workflow.add_conditional_edges(
    "search",
    lambda state: (
        "handleError" if state.phase == SearchPhase.ERROR 
        else "search" if (state.currentSearchIndex or 0) < len(state.searchQueries or [])
        else "scrape"  # Go to scrape when all searches complete (analyzing phase)
    ),
    {
        "handleError": "handleError",
        "search": "search",
        "scrape": "scrape"
    }
)
# Add conditional edges from scrape
workflow.add_conditional_edges(
    "scrape",
    lambda state: "handleError" if state.phase == SearchPhase.ERROR else "analyze",
    {
        "handleError": "handleError",   
        "analyze": "analyze"
    }
)

# Add conditional edges from analyze
workflow.add_conditional_edges(
    "analyze",
    lambda state: (
        "handleError" if state.phase == SearchPhase.ERROR
        else "plan" if state.phase == SearchPhase.PLANNING
        else "summarize"
    ),
    {
        "handleError": "handleError",
        "plan": "plan",
        "summarize": "summarize"
        
    }
)

# Add conditional edges from summarize
workflow.add_conditional_edges(
    "summarize",
    lambda state: "handleError" if state.phase == SearchPhase.ERROR else "formatter",
    {
        "handleError": "handleError",
        "formatter": "formatter"
    }
)

# Add conditional edges from formatter
workflow.add_conditional_edges(
    "formatter",
    lambda state: "handleError" if state.phase == SearchPhase.ERROR else "complete",
    {
        "handleError": "handleError",
        "complete": "complete"  
    }
)



# Add conditional edges from handleError
# temorary just end the graph
workflow.add_edge("handleError", END)
# workflow.add_conditional_edges(
#     "handleError",
#     lambda state: END if state.phase == SearchPhase.ERROR else "analyzer",
#     {
#         END: END,
#         "analyzer": "analyzer"
#     }
# )
workflow.add_edge("complete", END)


# Compile the graph
graph = workflow.compile()
graph.name = "OutbondSearchAgent" 


# png_image = graph.get_graph().draw_mermaid_png()

# with open("langgraph_diagram.png", "wb") as f:
#     f.write(png_image)
