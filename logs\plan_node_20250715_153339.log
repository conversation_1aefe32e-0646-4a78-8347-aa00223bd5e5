2025-07-15 15:33:39 - outbond.logging_config - INFO - setup_logging:77 - Logging configured - File: logs\plan_node_20250715_153339.log, Level: DEBUG
2025-07-15 15:33:39 - outbond.logging_config - INFO - setup_logging:78 - Log rotation: 10MB max, 5 backups
2025-07-15 15:33:39 - outbond.logging_config - INFO - setup_plan_node_logging:110 - Plan node logging configured with detailed debugging enabled
2025-07-15 15:33:39 - langchain_community.utils.user_agent - WARNING - get_user_agent:11 - USER_AGENT environment variable not set, consider setting it to identify your requests.
2025-07-15 15:33:39 - langgraph_api.graph - INFO - stop_remote_graphs:404 - {'event': 'Shutting down remote graphs', 'thread_name': 'MainThread', 'logger': 'langgraph_api.graph', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T13:33:39.519912Z'}
2025-07-15 15:33:39 - httpcore.connection - DEBUG - atrace:87 - close.started
2025-07-15 15:33:39 - httpcore.connection - DEBUG - atrace:87 - close.complete
2025-07-15 15:33:39 - uvicorn.error - ERROR - send:121 - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\starlette\routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph_runtime_inmem\lifespan.py", line 64, in lifespan
    await graph.collect_graphs_from_env(True)
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph_api\graph.py", line 386, in collect_graphs_from_env
    graph = await run_in_executor(None, _graph_from_spec, spec)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph_api\utils\config.py", line 135, in run_in_executor
    return await asyncio.get_running_loop().run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph_api\utils\config.py", line 126, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\uv\cache\archive-v0\6YF11-yvVix5nRhxaI3Nd\Lib\site-packages\langgraph_api\graph.py", line 431, in _graph_from_spec
    modspec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Dev_Lasngrapg\assesment\src/outbond/graph.py", line 7, in <module>
    from outbond.nodes import analyzer_node, handle_error_node, plan_node, search_node, scrape_node, analyze_node, complete_node, summarize_node
  File "C:\Dev_Lasngrapg\assesment\src\outbond\nodes\__init__.py", line 13, in <module>
    from .formatter import formatter_node
  File "C:\Dev_Lasngrapg\assesment\src\outbond\nodes\formatter.py", line 47
    message = {"schema":json.dumps(json_schema, indent=2),"final_answer",final_answer}
                                                                       ^
SyntaxError: ':' expected after dictionary key

2025-07-15 15:33:39 - uvicorn.error - ERROR - startup:59 - Application startup failed. Exiting.
