"""Scrape node for scraping URLs that need additional content."""

import async<PERSON>
from typing import List, Optional



from outbond.configuration import Configuration


from outbond.langchain_crawler import LangChainCrawler
from outbond.nodes.search import score_content, summarize_content
from shared.llm_factory import create_llm
from outbond.state import SEARCH_CONFIG, ErrorType, SearchPhase, SearchState, Source


async def scrape_node(state: SearchState, config: Optional[Configuration] = None) -> SearchState:
    """Scrape node that scrapes URLs needing additional content.
    
    This implementation:
    1. Filters sources that need scraping (missing content or content too short)
    2. Passes through sources that already have sufficient content
    3. Scrapes filtered sources using Firecrawl
    4. Scores and summarizes scraped content
    5. Returns all sources (existing + newly scraped)
    
    Args:
        state: The current SearchState
        config: Optional configuration
        
    Returns:
        Updated SearchState with scraped content processed
    """
    # Initialize LangChainCrawler client
    crawler = LangChainCrawler()
    
    # Filter sources that need scraping
    sources_to_scrape = [
        s for s in (state.sources or [])
        if not s.content or len(s.content) < SEARCH_CONFIG.MIN_CONTENT_LENGTH
    ]
    
    # Sources with sufficient content - pass them through
    sources_with_content = [
        s for s in (state.sources or [])
        if s.content and len(s.content) >= SEARCH_CONFIG.MIN_CONTENT_LENGTH
    ]
    
    new_scraped_sources: List[Source] = []
    new_scraped_sources.extend(sources_with_content)
    
    # Limit the number of sources to scrape
    sources_to_process = sources_to_scrape[:SEARCH_CONFIG.MAX_SOURCES_TO_SCRAPE]
    
    # Initialize LLM for summarization
    llm = create_llm()
    
    # Scrape sources without sufficient content
    for i, source in enumerate(sources_to_process):
        try:
            # Scrape the URL
            scraped_result = await crawler.scrape_url(
                source.url, 
                timeout_ms=SEARCH_CONFIG.SCRAPE_TIMEOUT
            )
            
            if scraped_result.get('success') and scraped_result.get('markdown'):
                # Create enriched source with scraped content
                enriched_source = Source(
                    url=source.url,
                    title=source.title,
                    content=scraped_result['markdown'],
                    quality=score_content(scraped_result['markdown'], state.query)
                )
                
                new_scraped_sources.append(enriched_source)
                
                # Add small delay for rate limiting
                await asyncio.sleep(0.15)  # 150ms delay
                
                # Generate summary for the scraped content
                summary = await summarize_content(
                    scraped_result['markdown'], 
                    state.query, 
                    llm
                )
                
                if summary:
                    enriched_source.summary = summary
            
            elif scraped_result.get('error') == 'timeout':
                print(f"Scraping timed out for {source.url}")
                # Add the original source even if scraping failed
                new_scraped_sources.append(source)
                
            else:
                print(f"Scraping failed for {source.url}: {scraped_result.get('error', 'Unknown error')}")
                # Add the original source even if scraping failed
                new_scraped_sources.append(source)
                
        except Exception as scrape_error:
            try:
                hostname = source.url.split('/')[2] if '://' in source.url else source.url
                print(f"Couldn't access {hostname}, trying other sources...")
            except Exception:
                print(f"Couldn't access {source.url}, trying other sources...")
                
            # Add the original source even if scraping failed
            new_scraped_sources.append(source)
    
    # Return updated state with scraped sources and move to analyzing phase
    return SearchState(
        query=state.query,
        context=state.context,
        understanding=state.understanding,
        searchQueries=state.searchQueries,
        currentSearchIndex=state.currentSearchIndex,
        sources=state.sources,  # Keep original sources
        scrapedSources=new_scraped_sources,  # Set the scraped sources
        processedSources=state.processedSources,
        finalAnswer=state.finalAnswer,
        followUpQuestions=state.followUpQuestions,
        subQueries=state.subQueries,
        searchAttempt=state.searchAttempt,
        retryCount=state.retryCount,
        phase=SearchPhase.ANALYZING,  # Move to analyzing phase
        error=state.error,
        errorType=state.errorType,
        maxRetries=state.maxRetries
    )
        
   