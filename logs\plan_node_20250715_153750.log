2025-07-15 15:37:50 - outbond.logging_config - INFO - setup_logging:77 - Logging configured - File: logs\plan_node_20250715_153750.log, Level: DEBUG
2025-07-15 15:37:50 - outbond.logging_config - INFO - setup_logging:78 - Log rotation: 10MB max, 5 backups
2025-07-15 15:37:50 - outbond.logging_config - INFO - setup_plan_node_logging:110 - Plan node logging configured with detailed debugging enabled
2025-07-15 15:37:50 - langchain_community.utils.user_agent - WARNING - get_user_agent:11 - USER_AGENT environment variable not set, consider setting it to identify your requests.
2025-07-15 15:37:50 - langgraph_api.graph - INFO - register_graph:56 - {'graph_id': 'outbound', 'event': "Registering graph with id 'outbound'", 'thread_name': 'asyncio_1', 'logger': 'langgraph_api.graph', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T13:37:50.629237Z'}
2025-07-15 15:37:50 - langgraph_runtime_inmem.queue - INFO - queue:72 - {'event': 'Starting 1 background workers', 'thread_name': 'asyncio_0', 'logger': 'langgraph_runtime_inmem.queue', 'level': 'info', 'api_variant': 'local_dev', 'langgraph_api_version': '0.2.86', 'timestamp': '2025-07-15T13:37:50.772634Z'}
